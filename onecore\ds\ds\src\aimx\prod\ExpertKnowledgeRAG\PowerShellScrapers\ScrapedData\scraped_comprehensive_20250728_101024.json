{"ScrapedAt": "2025-07-28T10:10:24Z", "BatchId": "20250728_101024", "Patterns": [{"RelevanceScore": 1, "CreatedAt": "2025-07-28T10:10:15Z", "PatternType": "common_mistake", "Id": "pattern_user_management_b195e7dd", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:15Z", "BestPractices": [], "Title": "Microsoft Docs: Get-<PERSON>User", "CodeTemplate": "$&lt;localUserObject&gt;", "CommonMistakes": [], "Domain": "user_management", "RequiredParameters": "[\"Get-ADUser (ActiveDirectory) | Microsoft Learn\\u003c/title\\u003e\",\"get-aduser?view=windowsserver2025-ps\\\" /\\u003e\",\"Get-ADUser (ActiveDirectory)\\\" /\\u003e\",\"Get-ADUser\\\" /\\u003e\",\"get-aduser?view=windowsserver2025-ps\\u0026amp;wt.mc_id=ps-gethelp\\\" /\\u003e\",\"Get-ADUser.md\\\" /\\u003e\",\"get-aduser\\\" /\\u003e\",\"Get-ADUser.md\\\"\",\"Get-ADUser\\u003c/h1\\u003e\",\"Get-ADUser\",\"Get-ADUser\\u003c/strong\\u003e cmdlet gets a specified user object or performs a search to get multiple user objects.\\u003c/p\\u003e\",\"Get-ADUser -Filter * -SearchBase \\\"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\\\"\",\"Get-ADUser -Filter \\u0027Name -like \\\"*SvcAccount\\\"\\u0027 | Format-Table Name,SamAccountName -A\",\"Get-ADUser -Identity ChewDavid -Properties *\",\"Get-ADUser -Filter \\\"Name -eq \\u0027ChewDavid\\u0027\\\" -SearchBase \\\"DC=AppNC\\\" -Properties \\\"mail\\\" -Server lds.Fabrikam.com:50000\",\"Get-ADUser -LDAPFilter \\u0027(!userAccountControl:1.2.840.113556.1.4.803:=2)\\u0027\",\"Get-ADUser -Filter \\\"Name -like \\u0027$UserName\\u0027\\\"\\u003c/strong\\u003e. On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: \\u003cstrong\\u003eGet-ADUser -Filter {Name -like $UserName}\\u003c/strong\\u003e.\\u003c/p\\u003e\",\"Get-ADUser\\u003c/code\\u003e\\u0026lt;user\\u0026gt;\\u003ccode\\u003e| Get-Member\\u003c/code\\u003e\\u003c/p\\u003e\",\"Get-ADUser\\u003c/code\\u003e\\u0026lt;user\\u0026gt;\\u003ccode\\u003e-Properties Extended | Get-Member\\u003c/code\\u003e\\u003c/p\\u003e\",\"Get-ADUser\\u003c/code\\u003e\\u0026lt;user\\u0026gt;\\u003ccode\\u003e-Properties * | Get-Member\\u003c/code\\u003e\\u003c/p\\u003e\",\"set-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eSet-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"new-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eNew-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"remove-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"move-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\"]", "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "user_management", "read"], "Abstract": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Get-ADUser (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" content...", "Sources": [{"SourceType": "microsoft_docs", "CredibilityScore": 0.95, "PublishedAt": "2025-07-28T10:10:15Z", "Url": "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser", "ScrapedAt": "2025-07-28T10:10:15Z", "Id": "bfff1159-17c9-41d8-8e75-ebc4f938801c", "Author": "Microsoft", "Title": "Microsoft Docs: Get-<PERSON>User"}], "Content": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Get-ADUser (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" content=\"light dark\" />\n\n\t\t\t<meta name=\"description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<link rel=\"canonical\" href=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps\" /> \n\n\t\t\t<!-- Non-customizable open graph and sharing-related metadata -->\n\t\t\t<meta name=\"twitter:card\" content=\"summary_large_image\" />\n\t\t\t<meta name=\"twitter:site\" content=\"@MicrosoftLearn\" />\n\t\t\t<meta property=\"og:type\" content=\"website\" />\n\t\t\t<meta property=\"og:image:alt\" content=\"Microsoft Learn\" />\n\t\t\t<meta property=\"og:image\" content=\"https://learn.microsoft.com/en-us/media/open-graph-image.png\" />\n\t\t\t<!-- Page specific open graph and sharing-related metadata -->\n\t\t\t<meta property=\"og:title\" content=\"Get-ADUser (ActiveDirectory)\" />\n\t\t\t<meta property=\"og:url\" content=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps\" />\n\t\t\t<meta property=\"og:description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<meta name=\"platform_id\" content=\"12007bea-81cb-fad7-d52a-dbb3ada967bf\" /> \n\t\t\t<meta name=\"locale\" content=\"en-us\" />\n\t\t\t <meta name=\"adobe-target\" content=\"true\" />\n\t\t\t<meta name=\"uhfHeaderId\" content=\"MSDocsHeader-M365-IT\" />\n\n\t\t\t<meta name=\"page_type\" content=\"powershell\" />\n\n\t\t\t<!--page specific meta tags-->\n\t\t\t\n\n\t\t\t<!-- custom meta tags -->\n\t\t\t\n\t\t<meta name=\"uid\" content=\"ActiveDirectory.Get-ADUser\" />\n\t\n\t\t<meta name=\"module\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"schema\" content=\"PowerShellCmdlet1\" />\n\t\n\t\t<meta name=\"ROBOTS\" content=\"INDEX, FOLLOW\" />\n\t\n\t\t<meta name=\"apiPlatform\" content=\"powershell\" />\n\t\n\t\t<meta name=\"archive_url\" content=\"https://learn.microsoft.com/previous-versions/powershell/windows/get-started\" />\n\t\n\t\t<meta name=\"author\" content=\"robinharwood\" />\n\t\n\t\t<meta name=\"breadcrumb_path\" content=\"/powershell/windows/bread/toc.json\" />\n\t\n\t\t<meta name=\"feedback_product_url\" content=\"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\" />\n\t\n\t\t<meta name=\"feedback_system\" content=\"Standard\" />\n\t\n\t\t<meta name=\"manager\" content=\"tedhudek\" />\n\t\n\t\t<meta name=\"ms.author\" content=\"roharwoo\" />\n\t\n\t\t<meta name=\"ms.devlang\" content=\"powershell\" />\n\t\n\t\t<meta name=\"ms.service\" content=\"windows-11\" />\n\t\n\t\t<meta name=\"ms.topic\" content=\"reference\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf\" />\n\t\n\t\t<meta name=\"document type\" content=\"cmdlet\" />\n\t\n\t\t<meta name=\"external help file\" content=\"Microsoft.ActiveDirectory.Management.dll-Help.xml\" />\n\t\n\t\t<meta name=\"HelpUri\" content=\"https://learn.microsoft.com/powershell/module/activedirectory/get-aduser?view=windowsserver2025-ps&amp;wt.mc_id=ps-gethelp\" />\n\t\n\t\t<meta name=\"Module Name\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"ms.date\" content=\"2016-12-27T00:00:00Z\" />\n\t\n\t\t<meta name=\"PlatyPS schema version\" content=\"2024-05-01T00:00:00Z\" />\n\t\n\t\t<meta name=\"document_id\" content=\"7b1c4ac3-4780-50a5-03fb-07a9c30bb1e7\" />\n\t\n\t\t<meta name=\"document_version_independent_id\" content=\"77dffbab-e12c-8cc1-9ab7-9fcab4d8b7a4\" />\n\t\n\t\t<meta name=\"updated_at\" content=\"2025-05-14T22:44:00Z\" />\n\t\n\t\t<meta name=\"original_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\" />\n\t\n\t\t<meta name=\"gitcommit\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\" />\n\t\n\t\t<meta name=\"git_commit_id\" content=\"0ef3f225d29e26d1cf3119f37dfff70bb6165746\" />\n\t\n\t\t<meta name=\"monikers\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"default_moniker\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"site_name\" content=\"Docs\" />\n\t\n\t\t<meta name=\"depot_name\" content=\"TechNet.windows-powershell\" />\n\t\n\t\t<meta name=\"in_right_rail\" content=\"h2h3\" />\n\t\n\t\t<meta name=\"page_kind\" content=\"command\" />\n\t\n\t\t<meta name=\"toc_rel\" content=\"../windowsserver2025-ps/toc.json\" />\n\t\n\t\t<meta name=\"feedback_help_link_type\" content=\"\" />\n\t\n\t\t<meta name=\"feedback_help_link_url\" content=\"\" />\n\t\n\t\t<meta name=\"config_moniker_range\" content=\"WindowsServer2025-ps\" />\n\t\n\t\t<meta name=\"asset_id\" content=\"module/activedirectory/get-aduser\" />\n\t\n\t\t<meta name=\"moniker_range_name\" content=\"ffb05b7b47577225af7c7b6a20151268\" />\n\t\n\t\t<meta name=\"item_type\" content=\"Content\" />\n\t\n\t\t<meta name=\"source_path\" content=\"docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\" />\n\t\n\t\t<meta name=\"github_feedback_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\" />\n\t \n\t\t<meta name=\"cmProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/bcbcbad5-4208-4783-8035-8481272c98b8\" data-source=\"generated\" />\n\t\n\t\t<meta name=\"spProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8\" data-source=\"generated\" />\n\t\n\n\t\t\t<!-- assets and js globals -->\n\t\t\t\n\t\t\t<link rel=\"stylesheet\" href=\"/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css\" />\n\t\t\t<link rel=\"preconnect\" href=\"//mscom.demdex.net\" crossorigin />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//target.microsoft.com\" />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//microsoftmscompoc.tt.omtrdc.net\" />\n\t\t\t\t\t\t<link\n\t\t\t\t\t\t\trel=\"preload\"\n\t\t\t\t\t\t\tas=\"script\"\n\t\t\t\t\t\t\thref=\"/static/third-party/adobe-target/at-js/2.9.0/at.js\"\n\t\t\t\t\t\t\tintegrity=\"sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu\"\n\t\t\t\t\t\t\tcrossorigin=\"anonymous\"\n\t\t\t\t\t\t\tid=\"adobe-target-script\"\n\t\t\t\t\t\t\ttype=\"application/javascript\"\n\t\t\t\t\t\t/>\n\t\t\t<script src=\"https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js\"></script>\n\t\t\t<script src=\"https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js\"></script>\n\t\t\t<script src=\"/_themes/docs.theme/master/en-us/_themes/global/deprecation.js\"></script>\n\n\t\t\t<!-- msdocs global object -->\n\t\t\t<script id=\"msdocs-script\">\n\t\tvar msDocs = {\n  \"environment\": {\n    \"accessLevel\": \"online\",\n    \"azurePortalHostname\": \"portal.azure.com\",\n    \"reviewFeatures\": false,\n    \"supportLevel\": \"production\",\n    \"systemContent\": true,\n    \"siteName\": \"learn\",\n    \"legacyHosting\": false\n  },\n  \"data\": {\n    \"contentLocale\": \"en-us\",\n    \"contentDir\": \"ltr\",\n    \"userLocale\": \"en-us\",\n    \"userDir\": \"ltr\",\n    \"pageTemplate\": \"Reference\",\n    \"brand\": \"\",\n    \"context\": {},\n    \"standardFeedback\": true,\n    \"showFeedbackReport\": false,\n    \"feedbackHelpLinkType\": \"\",\n    \"feedbackHelpLinkUrl\": \"\",\n    \"feedbackSystem\": \"Standard\",\n    \"feedbackGitHubRepo\": \"\",\n    \"feedbackProductUrl\": \"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\",\n    \"extendBreadcrumb\": true,\n    \"isEditDisplayable\": true,\n    \"isPrivateUnauthorized\": false,\n    \"hideViewSource\": false,\n    \"isPermissioned\": false,\n    \"hasRecommendations\": false,\n    \"contributors\": []\n  },\n  \"functions\": {}\n};;\n\t</script>\n\n\t\t\t<!-- base scripts, msdocs global should be before this -->\n\t\t\t<script src=\"/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js\"></script>\n\t\t\t\n\n\t\t\t<!-- json-ld -->\n\t\t\t\n\t\t</head>\n\t\n\t\t\t<body\n\t\t\t\tid=\"body\"\n\t\t\t\tdata-bi-name=\"body\"\n\t\t\t\tclass=\"layout-body \"\n\t\t\t\tlang=\"en-us\"\n\t\t\t\tdir=\"ltr\"\n\t\t\t>\n\t\t\t\t<header class=\"layout-body-header\">\n\t\t<div class=\"header-holder has-default-focus\">\n\t\t\t\n\t\t<a\n\t\t\thref=\"#main\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to main content\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#side-doc-outline\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to in-page navigation\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#\"\n\t\t\tdata-skip-to-ask-learn\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\thidden\n\t\t>\n\t\t\tSkip to Ask Learn chat experience\n\t\t</a>\n\t\n\n\t\t\t<div hidden id=\"cookie-consent-holder\" data-test-id=\"cookie-consent-container\"></div>\n\t\t\t<!-- Unsupported browser warning -->\n\t\t\t<div\n\t\t\t\tid=\"unsupported-browser\"\n\t\t\t\tstyle=\"background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;\"\n\t\t\t\thidden\n\t\t\t>\n\t\t\t\t<div style=\"max-width: 800px; margin: 0 auto;\">\n\t\t\t\t\t<p style=\"font-size: 24px\">This browser is no longer supported.</p>\n\t\t\t\t\t<p style=\"font-size: 16px; margin-top: 16px;\">\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t</p>\n\t\t\t\t\t<div style=\"margin-top: 12px;\">\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://go.microsoft.com/fwlink/p/?LinkID=2092881 \"\n\t\t\t\t\t\t\tstyle=\"background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tDownload Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge\"\n\t\t\t\t\t\t\tstyle=\"background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tMore info about Internet Explorer and Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<!-- site header -->\n\t\t\t<header\n\t\t\t\tid=\"ms--site-header\"\n\t\t\t\tdata-test-id=\"site-header-wrapper\"\n\t\t\t\trole=\"banner\"\n\t\t\t\titemscope=\"itemscope\"\n\t\t\t\titemtype=\"http://schema.org/Organization\"\n\t\t\t>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--mobile-nav\"\n\t\t\t\t\tclass=\"site-header display-none-tablet padding-inline-none gap-none\"\n\t\t\t\t\tdata-bi-name=\"mobile-header\"\n\t\t\t\t\tdata-test-id=\"mobile-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--primary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L1-header\"\n\t\t\t\t\tdata-test-id=\"primary-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--secondary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L2-header\"\n\t\t\t\t\tdata-test-id=\"secondary-header\"\n\t\t\t\t></div>\n\t\t\t</header>\n\t\t\t\n\t\t<!-- banner -->\n\t\t<div data-banner>\n\t\t\t<div id=\"disclaimer-holder\"></div>\n\t\t\t\n\t\t</div>\n\t\t<!-- banner end -->\n\t\n\t\t</div>\n\t</header>\n\t\t\t\t <section\n\t\t\t\t\tid=\"layout-body-menu\"\n\t\t\t\t\tclass=\"layout-body-menu display-flex\"\n\t\t\t\t\tdata-bi-name=\"menu\"\n\t\t\t  >\n\t\t\t\t\t<div\n\t\tid=\"left-container\"\n\t\tclass=\"left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full\"\n\t>\n\t\t<nav\n\t\t\tid=\"affixed-left-container\"\n\t\t\tclass=\"margin-top-sm-tablet position-sticky display-flex flex-direction-column\"\n\t\t\taria-label=\"Primary\"\n\t\t></nav>\n\t</div>\n\t\t\t  </section>\n\n\t\t\t\t<main\n\t\t\t\t\tid=\"main\"\n\t\t\t\t\trole=\"main\"\n\t\t\t\t\tclass=\"layout-body-main \"\n\t\t\t\t\tdata-bi-name=\"content\"\n\t\t\t\t\tlang=\"en-us\"\n\t\t\t\t\tdir=\"ltr\"\n\t\t\t\t>\n\t\t\t\t\t\n\t\t\t<div\n\t\tid=\"ms--content-header\"\n\t\tclass=\"content-header default-focus border-bottom-none\"\n\t\tdata-bi-name=\"content-header\"\n\t>\n\t\t<div class=\"content-header-controls margin-xxs margin-inline-sm-tablet\">\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"contents-button button button-sm margin-right-xxs\"\n\t\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\tdata-contents-button\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-menu\"></span></span>\n\t\t\t\t<span class=\"contents-expand-title\"> Table of contents </span>\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"ap-collapse-behavior ap-expanded button button-sm\"\n\t\t\t\tdata-bi-name=\"ap-collapse\"\n\t\t\t\taria-controls=\"action-panel\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-exit-mode\"></span></span>\n\t\t\t\t<span>Exit editor mode</span>\n\t\t\t</button>\n\t\t</div>\n\t</div>\n\t\t\t<div data-main-column class=\"padding-sm padding-top-none padding-top-sm-tablet\">\n\t\t\t\t<div>\n\t\t\t\t\t\n\t\t<div id=\"article-header\" class=\"background-color-body margin-bottom-xs display-none-print\">\n\t\t\t<div class=\"display-flex align-items-center justify-content-space-between\">\n\t\t\t\t\n\t\t<details\n\t\t\tid=\"article-header-breadcrumbs-overflow-popover\"\n\t\t\tclass=\"popover\"\n\t\t\tdata-for=\"article-header-breadcrumbs\"\n\t\t>\n\t\t\t<summary\n\t\t\t\tclass=\"button button-clear button-primary button-sm inner-focus\"\n\t\t\t\taria-label=\"All breadcrumbs\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-more\"></span>\n\t\t\t\t</span>\n\t\t\t</summary>\n\t\t\t<div id=\"article-header-breadcrumbs-overflow\" class=\"popover-content padding-none\"></div>\n\t\t</details>\n\n\t\t<bread-crumbs\n\t\t\tid=\"article-header-breadcrumbs\"\n\t\t\tdata-test-id=\"article-header-breadcrumbs\"\n\t\t\tclass=\"overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs\"\n\t\t></bread-crumbs>\n\t \n\t\t<div\n\t\t\tid=\"article-header-page-actions\"\n\t\t\tclass=\"opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch\"\n\t\t>\n\t\t\t\n\t\t<button\n\t\t\tclass=\"button button-sm border-none inner-focus display-none-tablet flex-shrink-0 \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-mobile\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-label=\"Ask Learn\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-tablet\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs\t \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-flyout-entry\"\n\t\t\tdata-ask-learn-flyout-entry\n\t\t\tdata-flyout-button=\"toggle\"\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-controls=\"ask-learn-flyout\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tid=\"ms--focus-mode-button\"\n\t\t\tdata-focus-mode\n\t\t\tdata-bi-name=\"focus-mode-entry\"\n\t\t\tclass=\"button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop\"\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-glasses\"></span>\n\t\t\t</span>\n\t\t\t<span>Focus mode</span>\n\t\t</button>\n\t \n\n\t\t\t<details class=\"popover popover-right\" id=\"article-header-page-actions-overflow\">\n\t\t\t\t<summary\n\t\t\t\t\tclass=\"justify-content-flex-start button button-clear button-sm button-primary inner-focus\"\n\t\t\t\t\taria-label=\"More actions\"\n\t\t\t\t\ttitle=\"More actions\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-more-vertical\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<div class=\"popover-content\">\n\t\t\t\t\t\n\t\t<button\n\t\t\tdata-page-action-item=\"overflow-mobile\"\n\t\t\ttype=\"button\"\n\t\t\tclass=\"button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\tdata-contents-button\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\">\n\t\t\t\t<span class=\"docon docon-editor-list-bullet\" aria-hidden=\"true\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"contents-expand-title\">Table of contents</span>\n\t\t</button>\n\t \n\t\t<a\n\t\t\tid=\"lang-link-overflow\"\n\t\t\tclass=\"button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"language-toggle\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-read-in-link\n\t\t\thref=\"#\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\" data-read-in-link-icon>\n\t\t\t\t<span class=\"docon docon-locale-globe\"></span>\n\t\t\t</span>\n\t\t\t<span data-read-in-link-text>Read in English</span>\n\t\t</a>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"collection\"\n\t\t\tdata-bi-name=\"collection\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"collection-status\">Add</span>\n\t\t</button>\n\t\n\t\t\t\t\t\n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"plan\"\n\t\t\tdata-bi-name=\"plan\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"plan-status\">Add to plan</span>\n\t\t</button>\n\t  \n\t\t<a\n\t\t\tdata-contenteditbtn\n\t\t\tclass=\"button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none\"\n\t\t\tdata-bi-name=\"edit\"\n\t\t\t\n\t\t\thref=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\"\n\t\t\tdata-original_content_git_url=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\"\n\t\t\tdata-original_content_git_url_template=\"{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Get-ADUser.md\"\n\t\t\tdata-pr_repo=\"\"\n\t\t\tdata-pr_branch=\"\"\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-edit-outline\"></span>\n\t\t\t</span>\n\t\t\t<span>Edit</span>\n\t\t</a>\n\t\n\t\t\t\t\t\n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<h4 class=\"font-size-sm padding-left-xxs\">Share via</h4>\n\t\t\n\t\t\t\t\t<a\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook\"\n\t\t\t\t\t\tdata-bi-name=\"facebook\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-facebook-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Facebook</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter\"\n\t\t\t\t\t\tdata-bi-name=\"twitter\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-text\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-xlogo-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>x.com</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin\"\n\t\t\t\t\t\tdata-bi-name=\"linkedin\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-linked-in-logo\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>LinkedIn</span>\n\t\t\t\t\t</a>\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email\"\n\t\t\t\t\t\tdata-bi-name=\"email\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-mail-message\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Email</span>\n\t\t\t\t\t</a>\n\t\t\t  \n\t \n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<button\n\t\t\tclass=\"button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\ttype=\"button\"\n\t\t\tdata-bi-name=\"print\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-popover-close\n\t\t\tdata-print-page\n\t\t\tdata-check-hidden=\"true\"\n\t\t>\n\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-print\"></span>\n\t\t\t</span>\n\t\t\t<span>Print</span>\n\t\t</button>\n\t\n\t\t\t\t</div>\n\t\t\t</details>\n\t\t</div>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<!-- azure disclaimer -->\n\t\t\t\t\t\n\t\t\t\t\t<!-- privateUnauthorizedTemplate is hidden by default -->\n\t\t\t\t\t\n\t\t<div unauthorized-private-section data-bi-name=\"permission-content-unauthorized-private\" hidden>\n\t\t\t<hr class=\"hr margin-top-xs margin-bottom-sm\" />\n\t\t\t<div class=\"notification notification-info\">\n\t\t\t\t<div class=\"notification-content\">\n\t\t\t\t\t<p class=\"margin-top-none notification-title\">\n\t\t\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t\t\t<span class=\"docon docon-exclamation-circle-solid\" aria-hidden=\"true\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Note</span>\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined not-authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-sign-in\" href=\"#\" data-bi-name=\"permission-content-sign-in\">signing in</a> or <a  class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<div class=\"content\"></div>\n\t\t\t\t\t \n\t\t\t\t\t<div class=\"content\"><h1 data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\" class=\"margin-bottom-xs\">Get-ADUser</h1>\n\n\t<div class=\"margin-block-xxs\">\n\t\t<ul class=\"metadata page-metadata align-items-center\" data-bi-name=\"page info\">\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t</ul>\n\t</div>\n\n<div class=\"metadata\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">\n\t\t<dl class=\"attributeList\">\n\t\t\t<dt>Module:</dt>\n\t\t\t<dd><a href=\"./?view=windowsserver2025-ps\" data-linktype=\"relative-path\">ActiveDirectory Module</a></dd>\n\t\t</dl>\n</div>\n\n<nav id=\"center-doc-outline\" class=\"doc-outline is-hidden-desktop display-none-print margin-bottom-sm\" data-bi-name=\"intopic toc\" aria-label=\"\">\n  <h2 class=\"title is-6 margin-block-xs\"></h2>\n</nav>\n\n\n\t<div class=\"margin-block-sm\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">\n\t\t<p>Gets one or more Active Directory users.</p>\n\n\t</div>\n\n\t<h2 id=\"syntax\" data-chunk-ids=\"filter,identity,ldapfilter\">Syntax</h2>\n\t<h3 id=\"filter\" data-chunk-ids=\"filter\">\n\t\tFilter (Default)\n\t</h3>\n\t<div data-chunk-ids=\"filter\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADUser\n    -Filter &lt;String&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-ResultPageSize &lt;Int32&gt;]\n    [-ResultSetSize &lt;Int32&gt;]\n    [-SearchBase &lt;String&gt;]\n    [-SearchScope &lt;ADSearchScope&gt;]\n    [-Server &lt;String&gt;]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\t<h3 id=\"identity\" data-chunk-ids=\"identity\">\n\t\tIdentity\n\t</h3>\n\t<div data-chunk-ids=\"identity\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADUser\n    [-Identity] &lt;ADUser&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Partition &lt;String&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-Server &lt;String&gt;]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\t<h3 id=\"ldapfilter\" data-chunk-ids=\"ldapfilter\">\n\t\tLdap<wbr>Filter\n\t</h3>\n\t<div data-chunk-ids=\"ldapfilter\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADUser\n    -LDAPFilter &lt;String&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-ResultPageSize &lt;Int32&gt;]\n    [-ResultSetSize &lt;Int32&gt;]\n    [-SearchBase &lt;String&gt;]\n    [-SearchScope &lt;ADSearchScope&gt;]\n    [-Server &lt;String&gt;]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\n\n\t<h2 id=\"description\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">Description</h2>\n\t<div data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">\n\t\t<p>The <strong>Get-ADUser</strong> cmdlet gets a specified user object or performs a search to get multiple user objects.</p>\n<p>The <em>Identity</em> parameter specifies the Active Directory user to get.\nYou can identify a user by its distinguished name (DN), GUID, security identifier (SID), or Security Account Manager (SAM) account name.\nYou can also set the parameter to a user object variable such as <code>$&lt;localUserObject&gt;</code> or pass a user object through the pipeline to the <em>Identity</em> parameter.</p>\n<p>To search for and retrieve more than one user, use the <em>Filter</em> or <em>LDAPFilter</em> parameters.\nThe <em>Filter</em> parameter uses the PowerShell Expression Language to write query strings for Active Directory.\nPowerShell Expression Language syntax provides rich type-conversion support for value types received by the <em>Filter</em> parameter.\nFor more information about the <em>Filter</em> parameter syntax, type <code>Get-Help about_ActiveDirectory_Filter</code>.\nIf you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the <em>LDAPFilter</em> parameter.</p>\n<p>This cmdlet retrieves a default set of user object properties.\nTo retrieve additional properties use the <em>Properties</em> parameter.\nFor more information about how to determine the properties for user objects, see the <em>Properties</em> parameter description.</p>\n\n\t</div>\n\n\t<h2 id=\"examples\" data-chunk-ids=\"example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts\">Examples</h2>\n\t<h3 id=\"example-1-get-all-of-the-users-in-a-container\" data-chunk-ids=\"example-1-get-all-of-the-users-in-a-container\">Example 1: Get all of the users in a container</h3>\n\t<div data-chunk-ids=\"example-1-get-all-of-the-users-in-a-container\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Filter * -SearchBase \"OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM\"\n</code></pre>\n<p>This command gets all users in the container OU=Finance,OU=UserAccounts,DC=FABRIKAM,DC=COM.</p>\n\n\t</div>\n\t<h3 id=\"example-2-get-a-filtered-list-of-users\" data-chunk-ids=\"example-2-get-a-filtered-list-of-users\">Example 2: Get a filtered list of users</h3>\n\t<div data-chunk-ids=\"example-2-get-a-filtered-list-of-users\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Filter 'Name -like \"*SvcAccount\"' | Format-Table Name,SamAccountName -A\n</code></pre>\n<pre><code class=\"lang-Output\">Name             SamAccountName\n----             --------------\nSQL01 SvcAccount SQL01\nSQL02 SvcAccount SQL02\nIIS01 SvcAccount IIS01\n</code></pre>\n<p>This command gets all users that have a name that ends with SvcAccount.</p>\n\n\t</div>\n\t<h3 id=\"example-3-get-all-of-the-properties-for-a-specified-user\" data-chunk-ids=\"example-3-get-all-of-the-properties-for-a-specified-user\">Example 3: Get all of the properties for a specified user</h3>\n\t<div data-chunk-ids=\"example-3-get-all-of-the-properties-for-a-specified-user\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Identity ChewDavid -Properties *\n</code></pre>\n<pre><code class=\"lang-Output\">Surname           : David\nName              : Chew David\nUserPrincipalName :\nGivenName         : David\nEnabled           : False\nSamAccountName    : ChewDavid\nObjectClass       : user\nSID               : S-1-5-21-**********-**********-**********-3544\nObjectGUID        : e1418d64-096c-4cb0-b903-ebb66562d99d\nDistinguishedName : CN=Chew David,OU=NorthAmerica,OU=Sales,OU=UserAccounts,DC=FABRIKAM,DC=COM\n</code></pre>\n<p>This command gets all of the properties of the user with the SAM account name ChewDavid.</p>\n\n\t</div>\n\t<h3 id=\"example-4-get-a-specified-user\" data-chunk-ids=\"example-4-get-a-specified-user\">Example 4: Get a specified user</h3>\n\t<div data-chunk-ids=\"example-4-get-a-specified-user\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Filter \"Name -eq 'ChewDavid'\" -SearchBase \"DC=AppNC\" -Properties \"mail\" -Server lds.Fabrikam.com:50000\n</code></pre>\n<p>This command gets the user with the name ChewDavid in the Active Directory Lightweight Directory Services (AD LDS) instance.</p>\n\n\t</div>\n\t<h3 id=\"example-5-get-all-enabled-user-accounts\" data-chunk-ids=\"example-5-get-all-enabled-user-accounts\">Example 5: Get all enabled user accounts</h3>\n\t<div data-chunk-ids=\"example-5-get-all-enabled-user-accounts\">\n\t\t<pre><code class=\"lang-powershell\">C:\\PS&gt; Get-ADUser -LDAPFilter '(!userAccountControl:1.2.840.113556.1.4.803:=2)'\n</code></pre>\n<p>This command gets all enabled user accounts in Active Directory using an LDAP filter.</p>\n\n\t</div>\n\n\t<h2 id=\"parameters\" data-chunk-ids=\"authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">Parameters</h2>\n\t\t<h3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Auth<wbr>Type</h3>\n\t\t<p>Specifies the authentication method to use.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>Negotiate or 0</li>\n<li>Basic or 1</li>\n</ul>\n<p>The default authentication method is Negotiate.</p>\n<p>A Secure Sockets Layer (SSL) connection is required for the Basic authentication method.</p>\n\n\n\t\t<h4 id=\"authtype-properties\" data-chunk-ids=\"authtype\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"authtype\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADAuthType</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>Negotiate, Basic</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"authtype-sets\" data-chunk-ids=\"authtype\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Credential</h3>\n\t\t<p>Specifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.</p>\n<p>To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a <strong>PSCredential</strong> object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.</p>\n<p>You can also create a <strong>PSCredential</strong> object by using a script or by using the <strong>Get-Credential</strong> cmdlet.\nYou can then set the <em>Credential</em> parameter to the <strong>PSCredential</strong> object.</p>\n<p>If the acting credentials do not have directory-level permission to perform the task, Active Directory PowerShell returns a terminating error.</p>\n\n\n\t\t<h4 id=\"credential-properties\" data-chunk-ids=\"credential\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"credential\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">PSCredential</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"credential-sets\" data-chunk-ids=\"credential\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-filter\" data-chunk-ids=\"filter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Filter</h3>\n\t\t<p>Specifies a query string that retrieves Active Directory objects.\nThis string uses the PowerShell Expression Language syntax.\nThe PowerShell Expression Language syntax provides rich type-conversion support for value types received by the <em>Filter</em> parameter.\nThe syntax uses an in-order representation, which means that the operator is placed between the operand and the value.\nFor more information about the <em>Filter</em> parameter, type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>\n<p>Syntax:</p>\n<p>The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter.</p>\n<p>&lt;filter&gt;  ::= \"{\" &lt;FilterComponentList&gt; \"}\"</p>\n<p>&lt;FilterComponentList&gt; ::= &lt;FilterComponent&gt; | &lt;FilterComponent&gt; &lt;JoinOperator&gt; &lt;FilterComponent&gt; | &lt;NotOperator&gt;  &lt;FilterComponent&gt;</p>\n<p>&lt;FilterComponent&gt; ::= &lt;attr&gt; &lt;FilterOperator&gt; &lt;value&gt; | \"(\" &lt;FilterComponent&gt; \")\"</p>\n<p>&lt;FilterOperator&gt; ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\"</p>\n<p>&lt;JoinOperator&gt; ::= \"-and\" | \"-or\"</p>\n<p>&lt;NotOperator&gt; ::= \"-not\"</p>\n<p>&lt;attr&gt; ::= &lt;PropertyName&gt; | &lt;LDAPDisplayName of the attribute&gt;</p>\n<p>&lt;value&gt;::= &lt;compare this value with an &lt;attr&gt; by using the specified &lt;FilterOperator&gt;&gt;</p>\n<p>For a list of supported types for &lt;value&gt;, type <code>Get-Help about_ActiveDirectory_ObjectModel</code>.</p>\n<p>Note: For String parameter type, PowerShell will cast the filter query to a string while processing the command. When using a string variable as a value in the filter component, make sure that it complies with the <a href=\"/en-us/powershell/module/microsoft.powershell.core/about/about_quoting_rules\" data-linktype=\"absolute-path\">PowerShell Quoting Rules</a>. For example, if the filter expression is double-quoted, the variable should be enclosed using single quotation marks:\n<strong>Get-ADUser -Filter \"Name -like '$UserName'\"</strong>. On the contrary, if curly braces are used to enclose the filter, the variable should not be quoted at all: <strong>Get-ADUser -Filter {Name -like $UserName}</strong>.</p>\n<p>Note: PowerShell wildcards other than *, such as ?, are not supported by the <em>Filter</em> syntax.</p>\n<p>Note: To query using LDAP query strings, use the <em>LDAPFilter</em> parameter.</p>\n\n\n\t\t<h4 id=\"filter-properties\" data-chunk-ids=\"filter\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"filter\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"filter-sets\" data-chunk-ids=\"filter\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"filter\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Identity</h3>\n\t\t<p>Specifies an Active Directory user object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>A distinguished name</li>\n<li>A GUID (objectGUID)</li>\n<li>A security identifier (objectSid)</li>\n<li>A SAM account name (sAMAccountName)</li>\n</ul>\n<p>The cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.</p>\n<p>This parameter can also get this object through the pipeline or you can set this parameter to an object instance.</p>\n\n\n\t\t<h4 id=\"identity-properties\" data-chunk-ids=\"identity\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"identity\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADUser</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"identity-sets\" data-chunk-ids=\"identity\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>0</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-ldapfilter\" data-chunk-ids=\"ldapfilter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-LDAPFilter</h3>\n\t\t<p>Specifies an LDAP query string that is used to filter Active Directory objects.\nYou can use this parameter to run your existing LDAP queries.\nThe <em>Filter</em> parameter syntax supports the same functionality as the LDAP syntax.\nFor more information, see the <em>Filter</em> parameter description or type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>\n\n\n\t\t<h4 id=\"ldapfilter-properties\" data-chunk-ids=\"ldapfilter\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"ldapfilter\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"ldapfilter-sets\" data-chunk-ids=\"ldapfilter\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"ldapfilter\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-partition\" data-chunk-ids=\"partition\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Partition</h3>\n\t\t<p>Specifies the distinguished name of an Active Directory partition.\nThe distinguished name must be one of the naming contexts on the current directory server.\nThe cmdlet searches this partition to find the object defined by the <em>Identity</em> parameter.</p>\n<p>In many cases, a default value is used for the <em>Partition</em> parameter if no value is specified.\nThe rules for determining the default value are given below.\nNote that rules listed first are evaluated first, and when a default value can be determined, no further rules are evaluated.</p>\n<p>In AD DS environments, a default value for <em>Partition</em> is set in the following cases:</p>\n<ul>\n<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>\n<li>If none of the previous cases apply, the default value of <em>Partition</em> is set to the default partition or naming context of the target domain.</li>\n</ul>\n<p>In AD LDS environments, a default value for <em>Partition</em> is set in the following cases:</p>\n<ul>\n<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>\n<li>If the target AD LDS instance has a default naming context, the default value of <em>Partition</em> is set to the default naming context.\nTo specify a default naming context for an AD LDS environment, set the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent object (<strong>nTDSDSA</strong>) for the AD LDS instance.</li>\n<li>If none of the previous cases apply, the <em>Partition</em> parameter does not take any default value.</li>\n</ul>\n\n\n\t\t<h4 id=\"partition-properties\" data-chunk-ids=\"partition\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"partition\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"partition-sets\" data-chunk-ids=\"partition\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"partition\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-properties\" data-chunk-ids=\"properties\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Properties</h3>\n\t\t<p>Specifies the properties of the output object to retrieve from the server.\nUse this parameter to retrieve properties that are not included in the default set.</p>\n<p>Specify properties for this parameter as a comma-separated list of names.\nTo display all of the attributes that are set on the object, specify * (asterisk).</p>\n<p>To specify an individual extended property, use the name of the property.\nFor properties that are not default or extended properties, you must specify the LDAP display name of the attribute.</p>\n<p>To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the <strong>Get-Member</strong> cmdlet.</p>\n\n\n\t\t<h4 id=\"properties-properties\" data-chunk-ids=\"properties\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"properties\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><p><span class=\"no-loc xref\">String</span><span>[</span><span>]</span></p>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Aliases:</td><td>Property</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"properties-sets\" data-chunk-ids=\"properties\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"properties\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-resultpagesize\" data-chunk-ids=\"resultpagesize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Result<wbr>Page<wbr>Size</h3>\n\t\t<p>Specifies the number of objects to include in one page for an Active Directory Domain Services query.</p>\n<p>The default is 256 objects per page.</p>\n\n\n\t\t<h4 id=\"resultpagesize-properties\" data-chunk-ids=\"resultpagesize\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"resultpagesize\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Int32</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"resultpagesize-sets\" data-chunk-ids=\"resultpagesize\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-resultsetsize\" data-chunk-ids=\"resultsetsize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Result<wbr>Set<wbr>Size</h3>\n\t\t<p>Specifies the maximum number of objects to return for an Active Directory Domain Services query.\nIf you want to receive all of the objects, set this parameter to $Null (null value).\nYou can use Ctrl+C to stop the query and return of objects.</p>\n<p>The default is $Null.</p>\n\n\n\t\t<h4 id=\"resultsetsize-properties\" data-chunk-ids=\"resultsetsize\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"resultsetsize\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Int32</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"resultsetsize-sets\" data-chunk-ids=\"resultsetsize\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-searchbase\" data-chunk-ids=\"searchbase\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Search<wbr>Base</h3>\n\t\t<p>Specifies an Active Directory path to search under.</p>\n<p>When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive.</p>\n<p>When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain.</p>\n<p>When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent (DSA) object (<strong>nTDSDSA</strong>) for the AD LDS instance.\nIf no default naming context has been specified for the target AD LDS instance, then this parameter has no default value.</p>\n<p>When the value of the <em>SearchBase</em> parameter is set to an empty string and you are connected to a GC port, all partitions are searched.\nIf the value of the <em>SearchBase</em> parameter is set to an empty string and you are not connected to a GC port, an error is thrown.</p>\n\n\n\t\t<h4 id=\"searchbase-properties\" data-chunk-ids=\"searchbase\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"searchbase\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"searchbase-sets\" data-chunk-ids=\"searchbase\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchbase\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchbase\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-searchscope\" data-chunk-ids=\"searchscope\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Search<wbr>Scope</h3>\n\t\t<p>Specifies the scope of an Active Directory search.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>Base or 0</li>\n<li>OneLevel or 1</li>\n<li>Subtree or 2</li>\n</ul>\n<p>A SearchScope with a Base value searches only for the given user. If an OU is specified in the SearchBase parameter, no user will be returned by, for example, a specified Filter statement.\nA OneLevel query searches the immediate children of that path or object. This option only works when an OU is given as the SearchBase. If a user is given, no results are returned.\nA Subtree query searches the current path or object and all children of that path or object.</p>\n\n\n\t\t<h4 id=\"searchscope-properties\" data-chunk-ids=\"searchscope\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"searchscope\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADSearchScope</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>Base, OneLevel, Subtree</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"searchscope-sets\" data-chunk-ids=\"searchscope\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchscope\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchscope\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Server</h3>\n\t\t<p>Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server.\nThe service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.</p>\n<p>Domain name values:</p>\n<ul>\n<li>Fully qualified domain name (FQDN)</li>\n<li>NetBIOS name</li>\n</ul>\n<p>Directory server values:</p>\n<ul>\n<li>Fully qualified directory server name</li>\n<li>NetBIOS name</li>\n<li>Fully qualified directory server name and port</li>\n</ul>\n<p>The default value for the <em>Server</em> parameter is determined by one of the following methods in the order that they are listed:</p>\n<ul>\n<li>By using <em>Server</em> value from objects passed through the pipeline.</li>\n<li>By using the server information associated with the Active Directory PowerShell provider drive, when running under that drive.</li>\n<li>By using the domain of the computer running PowerShell.</li>\n</ul>\n\n\n\t\t<h4 id=\"server-properties\" data-chunk-ids=\"server\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"server\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"server-sets\" data-chunk-ids=\"server\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"common-parameters\" data-no-chunk=\"\">CommonParameters</h3>\n\t\t<div data-no-chunk=\"\">\n\t\t\t<p>This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n<a href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\">about_CommonParameters</a>.</p>\n\n\t\t</div>\n\n\t<h2 id=\"inputs\" data-chunk-ids=\"inputs\">Inputs</h2>\n\t\t\t<h3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">None or Microsoft.ActiveDirectory.Management.ADUser</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"inputs\">\n\t\t\t\t<p>A user object is received by the <em>Identity</em> parameter.</p>\n\n\t\t\t</div>\n\n\t<h2 id=\"outputs\" data-chunk-ids=\"outputs\">Outputs</h2>\n\t\t\t<h3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">Microsoft.ActiveDirectory.Management.ADUser</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"outputs\">\n\t\t\t\t<p>Returns one or more user objects.</p>\n<p>This cmdlet returns a default set of <strong>ADUser</strong> property values.\nTo retrieve additional <strong>ADUser</strong> properties, use the <em>Properties</em> parameter.</p>\n<p>To get a list of the default set of properties of an <strong>ADUser</strong> object, use the following command:</p>\n<p><code>Get-ADUser</code>&lt;user&gt;<code>| Get-Member</code></p>\n<p>To get a list of the most commonly used properties of an ADUser object, use the following command:</p>\n<p><code>Get-ADUser</code>&lt;user&gt;<code>-Properties Extended | Get-Member</code></p>\n<p>To get a list of all the properties of an <strong>ADUser</strong> object, use the following command:</p>\n<p><code>Get-ADUser</code>&lt;user&gt;<code>-Properties * | Get-Member</code></p>\n\n\t\t\t</div>\n\n\t<h2 id=\"notes\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">Notes</h2>\n\t<div data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-all-of-the-users-in-a-container,example-2-get-a-filtered-list-of-users,example-3-get-all-of-the-properties-for-a-specified-user,example-4-get-a-specified-user,example-5-get-all-enabled-user-accounts,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server\">\n\t\t<ul>\n<li>This cmdlet does not work with an Active Directory snapshot.</li>\n</ul>\n\n\t</div>\n\n\t<h2 id=\"related-links\" data-no-chunk=\"\">Related Links</h2>\n\t<ul data-no-chunk=\"\">\n\t\t\t<li><a href=\"new-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">New-ADUser</a></li>\n\t\t\t<li><a href=\"remove-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Remove-ADUser</a></li>\n\t\t\t<li><a href=\"set-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Set-ADUser</a></li>\n\t</ul>\n</div>\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t></div>\n\t \n\t\t<div\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\t<div\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\n\t\t\t\t\t\n\t\t<!-- feedback section -->\n\t\t<section\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t>\n\t\t\t<hr class=\"hr\" />\n\t\t\t<h2 id=\"ms--feedback\" class=\"title is-3\">Feedback</h2>\n\t\t\t<div class=\"display-flex flex-wrap-wrap align-items-center\">\n\t\t\t\t<p class=\"font-weight-semibold margin-xxs margin-left-none\">\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t</p>\n\t\t\t\t<div class=\"buttons\">\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Yes</span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>No</span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</section>\n\t\t<!-- end feedback section -->\n\t\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t</div>\n\t\t\t\n\t\t<div\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t></div>\n\t\n\t\t\n\t\t\t\t</main>\n\t\t\t\t<aside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  >\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t>\n\t\t\t<div id=\"affixed-right-container\" data-bi-name=\"right-column\">\n\t\t\t\t\n\t\t<nav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t>\n\t\t\t<h3>In this article</h3>\n\t\t</nav>\n\t\n\t\t\t\t<!-- Feedback -->\n\t\t\t\t\n\t\t<section\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t>\n\t\t\t<p class=\"font-weight-semibold margin-bottom-xs\">Was this page helpful?</p>\n\t\t\t<div class=\"buttons\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>Yes</span>\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>No</span>\n\t\t\t\t</button>\n\t\t\t</div>\n\t\t</section>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t  </aside> <section\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  >\n\t\t\t\t\t <div\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n></div>\n\t\t\t  </section> <div class=\"layout-body-footer \" data-bi-name=\"layout-footer\">\n\t\t<footer\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t>\n\t\t\t<div class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\">\n\t\t\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t><span class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t><span class=\"docon docon-world\"></span></span\n\t\t\t><span class=\"local-selector-link-text\">en-us</span></a\n\t\t>\n\t\n\t\t\t\t<div class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>\n\t\t<svg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t>\n\t\t\t<path\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t></path>\n\t\t</svg>\n\t\n\t\t\t<span>Your Privacy Choices</span></a\n\t\t>\n\t\n\t</div>\n\t\t\t\t<div class=\"flex-shrink-0\">\n\t\t<div class=\"dropdown has-caret-up\">\n\t\t\t<button\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-sun\" aria-hidden=\"true\"></span>\n\t\t\t\t</span>\n\t\t\t\t<span>Theme</span>\n\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t</span>\n\t\t\t</button>\n\t\t\t<div class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\">\n\t\t\t\t<ul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\">\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-light margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Light </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-dark margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Dark </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-high-contrast margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> High contrast </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>\n\t</div>\n\t\t\t</div>\n\t\t\t<ul class=\"links\" data-bi-name=\"footerlinks\">\n\t\t\t\t<li class=\"manage-cookies-holder\" hidden=\"\"></li>\n\t\t\t\t<li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>AI Disclaimer</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Previous Versions</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Blog</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Contribute</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Privacy</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Terms of Use</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Trademarks</a\n\t\t>\n\t\n\t</li>\n\t\t\t\t<li>&copy; Microsoft 2025</li>\n\t\t\t</ul>\n\t\t</footer>\n\t</footer>\n\t\t\t</body>\n\t\t</html>"}, {"RelevanceScore": 1, "CreatedAt": "2025-07-28T10:10:16Z", "PatternType": "common_mistake", "Id": "pattern_group_management_b960a674", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:16Z", "BestPractices": [], "Title": "Microsoft Docs: Get-ADGroup", "CodeTemplate": "$&lt;localGroupObject&gt;", "CommonMistakes": [], "Domain": "group_management", "RequiredParameters": "[\"Get-ADGroup (ActiveDirectory) | Microsoft Learn\\u003c/title\\u003e\",\"get-adgroup?view=windowsserver2025-ps\\\" /\\u003e\",\"Get-ADGroup (ActiveDirectory)\\\" /\\u003e\",\"Get-ADGroup\\\" /\\u003e\",\"get-adgroup?view=windowsserver2025-ps\\u0026amp;wt.mc_id=ps-gethelp\\\" /\\u003e\",\"Get-ADGroup.md\\\" /\\u003e\",\"get-adgroup\\\" /\\u003e\",\"Get-ADGroup.md\\\"\",\"Get-ADGroup\\u003c/h1\\u003e\",\"Get-ADGroup\",\"Get-ADGroup\\u003c/strong\\u003e cmdlet gets a group or performs a search to retrieve multiple groups from an Active Directory.\\u003c/p\\u003e\",\"Get-ADGroup -Identity Administrators\",\"Get-ADGroup -Identity S-1-5-32-544 -Properties member\",\"Get-ADGroup -Filter \\u0027GroupCategory -eq \\\"Security\\\" -and GroupScope -ne \\\"DomainLocal\\\"\\u0027\",\"Get-ADGroup -Server localhost:60000 -Filter \\\"GroupScope -eq \\u0027DomainLocal\\u0027\\\" -SearchBase \\\"DC=AppNC\\\"\",\"Get-ADGroup\\u003c/strong\\u003e cmdlet returns a default set of \\u003cstrong\\u003eADGroup\\u003c/strong\\u003e property values.\",\"Get-ADGroup\\u003c/code\\u003e\\u0026lt;group\\u0026gt;\\u003ccode\\u003e| Get-Member\\u003c/code\\u003e\\u003c/p\\u003e\",\"Get-ADGroup\\u003c/code\\u003e\\u0026lt;group\\u0026gt;\\u003ccode\\u003e-Properties * | Get-Member\\u003c/code\\u003e\\u003c/p\\u003e\",\"set-adgroup?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eSet-ADGroup\\u003c/a\\u003e\\u003c/li\\u003e\",\"new-adgroup?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eNew-ADGroup\\u003c/a\\u003e\\u003c/li\\u003e\",\"remove-adgroup?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADGroup\\u003c/a\\u003e\\u003c/li\\u003e\",\"move-adgroup?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADGroup\\u003c/a\\u003e\\u003c/li\\u003e\"]", "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "group_management", "read"], "Abstract": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Get-ADGroup (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" conten...", "Sources": [{"SourceType": "microsoft_docs", "CredibilityScore": 0.95, "PublishedAt": "2025-07-28T10:10:16Z", "Url": "https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup", "ScrapedAt": "2025-07-28T10:10:16Z", "Id": "e8f32a4e-4f29-48e6-8349-1aebacb21f72", "Author": "Microsoft", "Title": "Microsoft Docs: Get-ADGroup"}], "Content": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Get-ADGroup (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" content=\"light dark\" />\n\n\t\t\t<meta name=\"description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<link rel=\"canonical\" href=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup?view=windowsserver2025-ps\" /> \n\n\t\t\t<!-- Non-customizable open graph and sharing-related metadata -->\n\t\t\t<meta name=\"twitter:card\" content=\"summary_large_image\" />\n\t\t\t<meta name=\"twitter:site\" content=\"@MicrosoftLearn\" />\n\t\t\t<meta property=\"og:type\" content=\"website\" />\n\t\t\t<meta property=\"og:image:alt\" content=\"Microsoft Learn\" />\n\t\t\t<meta property=\"og:image\" content=\"https://learn.microsoft.com/en-us/media/open-graph-image.png\" />\n\t\t\t<!-- Page specific open graph and sharing-related metadata -->\n\t\t\t<meta property=\"og:title\" content=\"Get-ADGroup (ActiveDirectory)\" />\n\t\t\t<meta property=\"og:url\" content=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/get-adgroup?view=windowsserver2025-ps\" />\n\t\t\t<meta property=\"og:description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<meta name=\"platform_id\" content=\"584d724e-a6fd-0944-3ea2-e49a85ee0614\" /> \n\t\t\t<meta name=\"locale\" content=\"en-us\" />\n\t\t\t <meta name=\"adobe-target\" content=\"true\" />\n\t\t\t<meta name=\"uhfHeaderId\" content=\"MSDocsHeader-M365-IT\" />\n\n\t\t\t<meta name=\"page_type\" content=\"powershell\" />\n\n\t\t\t<!--page specific meta tags-->\n\t\t\t\n\n\t\t\t<!-- custom meta tags -->\n\t\t\t\n\t\t<meta name=\"uid\" content=\"ActiveDirectory.Get-ADGroup\" />\n\t\n\t\t<meta name=\"module\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"schema\" content=\"PowerShellCmdlet1\" />\n\t\n\t\t<meta name=\"ROBOTS\" content=\"INDEX, FOLLOW\" />\n\t\n\t\t<meta name=\"apiPlatform\" content=\"powershell\" />\n\t\n\t\t<meta name=\"archive_url\" content=\"https://learn.microsoft.com/previous-versions/powershell/windows/get-started\" />\n\t\n\t\t<meta name=\"author\" content=\"robinharwood\" />\n\t\n\t\t<meta name=\"breadcrumb_path\" content=\"/powershell/windows/bread/toc.json\" />\n\t\n\t\t<meta name=\"feedback_product_url\" content=\"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\" />\n\t\n\t\t<meta name=\"feedback_system\" content=\"Standard\" />\n\t\n\t\t<meta name=\"manager\" content=\"tedhudek\" />\n\t\n\t\t<meta name=\"ms.author\" content=\"roharwoo\" />\n\t\n\t\t<meta name=\"ms.devlang\" content=\"powershell\" />\n\t\n\t\t<meta name=\"ms.service\" content=\"windows-11\" />\n\t\n\t\t<meta name=\"ms.topic\" content=\"reference\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf\" />\n\t\n\t\t<meta name=\"document type\" content=\"cmdlet\" />\n\t\n\t\t<meta name=\"external help file\" content=\"Microsoft.ActiveDirectory.Management.dll-Help.xml\" />\n\t\n\t\t<meta name=\"HelpUri\" content=\"https://learn.microsoft.com/powershell/module/activedirectory/get-adgroup?view=windowsserver2025-ps&amp;wt.mc_id=ps-gethelp\" />\n\t\n\t\t<meta name=\"Module Name\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"ms.date\" content=\"2016-12-27T00:00:00Z\" />\n\t\n\t\t<meta name=\"PlatyPS schema version\" content=\"2024-05-01T00:00:00Z\" />\n\t\n\t\t<meta name=\"document_id\" content=\"58a82506-10f6-0b67-82bc-bc8b94c747e9\" />\n\t\n\t\t<meta name=\"document_version_independent_id\" content=\"bf05db71-5b39-7b17-4712-42e090ac4593\" />\n\t\n\t\t<meta name=\"updated_at\" content=\"2025-05-14T22:44:00Z\" />\n\t\n\t\t<meta name=\"original_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\" />\n\t\n\t\t<meta name=\"gitcommit\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\" />\n\t\n\t\t<meta name=\"git_commit_id\" content=\"0ef3f225d29e26d1cf3119f37dfff70bb6165746\" />\n\t\n\t\t<meta name=\"monikers\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"default_moniker\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"site_name\" content=\"Docs\" />\n\t\n\t\t<meta name=\"depot_name\" content=\"TechNet.windows-powershell\" />\n\t\n\t\t<meta name=\"in_right_rail\" content=\"h2h3\" />\n\t\n\t\t<meta name=\"page_kind\" content=\"command\" />\n\t\n\t\t<meta name=\"toc_rel\" content=\"../windowsserver2025-ps/toc.json\" />\n\t\n\t\t<meta name=\"feedback_help_link_type\" content=\"\" />\n\t\n\t\t<meta name=\"feedback_help_link_url\" content=\"\" />\n\t\n\t\t<meta name=\"config_moniker_range\" content=\"WindowsServer2025-ps\" />\n\t\n\t\t<meta name=\"asset_id\" content=\"module/activedirectory/get-adgroup\" />\n\t\n\t\t<meta name=\"moniker_range_name\" content=\"ffb05b7b47577225af7c7b6a20151268\" />\n\t\n\t\t<meta name=\"item_type\" content=\"Content\" />\n\t\n\t\t<meta name=\"source_path\" content=\"docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\" />\n\t\n\t\t<meta name=\"github_feedback_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\" />\n\t \n\t\t<meta name=\"spProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8\" data-source=\"generated\" />\n\t\n\n\t\t\t<!-- assets and js globals -->\n\t\t\t\n\t\t\t<link rel=\"stylesheet\" href=\"/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css\" />\n\t\t\t<link rel=\"preconnect\" href=\"//mscom.demdex.net\" crossorigin />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//target.microsoft.com\" />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//microsoftmscompoc.tt.omtrdc.net\" />\n\t\t\t\t\t\t<link\n\t\t\t\t\t\t\trel=\"preload\"\n\t\t\t\t\t\t\tas=\"script\"\n\t\t\t\t\t\t\thref=\"/static/third-party/adobe-target/at-js/2.9.0/at.js\"\n\t\t\t\t\t\t\tintegrity=\"sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu\"\n\t\t\t\t\t\t\tcrossorigin=\"anonymous\"\n\t\t\t\t\t\t\tid=\"adobe-target-script\"\n\t\t\t\t\t\t\ttype=\"application/javascript\"\n\t\t\t\t\t\t/>\n\t\t\t<script src=\"https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js\"></script>\n\t\t\t<script src=\"https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js\"></script>\n\t\t\t<script src=\"/_themes/docs.theme/master/en-us/_themes/global/deprecation.js\"></script>\n\n\t\t\t<!-- msdocs global object -->\n\t\t\t<script id=\"msdocs-script\">\n\t\tvar msDocs = {\n  \"environment\": {\n    \"accessLevel\": \"online\",\n    \"azurePortalHostname\": \"portal.azure.com\",\n    \"reviewFeatures\": false,\n    \"supportLevel\": \"production\",\n    \"systemContent\": true,\n    \"siteName\": \"learn\",\n    \"legacyHosting\": false\n  },\n  \"data\": {\n    \"contentLocale\": \"en-us\",\n    \"contentDir\": \"ltr\",\n    \"userLocale\": \"en-us\",\n    \"userDir\": \"ltr\",\n    \"pageTemplate\": \"Reference\",\n    \"brand\": \"\",\n    \"context\": {},\n    \"standardFeedback\": true,\n    \"showFeedbackReport\": false,\n    \"feedbackHelpLinkType\": \"\",\n    \"feedbackHelpLinkUrl\": \"\",\n    \"feedbackSystem\": \"Standard\",\n    \"feedbackGitHubRepo\": \"\",\n    \"feedbackProductUrl\": \"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\",\n    \"extendBreadcrumb\": true,\n    \"isEditDisplayable\": true,\n    \"isPrivateUnauthorized\": false,\n    \"hideViewSource\": false,\n    \"isPermissioned\": false,\n    \"hasRecommendations\": false,\n    \"contributors\": []\n  },\n  \"functions\": {}\n};;\n\t</script>\n\n\t\t\t<!-- base scripts, msdocs global should be before this -->\n\t\t\t<script src=\"/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js\"></script>\n\t\t\t\n\n\t\t\t<!-- json-ld -->\n\t\t\t\n\t\t</head>\n\t\n\t\t\t<body\n\t\t\t\tid=\"body\"\n\t\t\t\tdata-bi-name=\"body\"\n\t\t\t\tclass=\"layout-body \"\n\t\t\t\tlang=\"en-us\"\n\t\t\t\tdir=\"ltr\"\n\t\t\t>\n\t\t\t\t<header class=\"layout-body-header\">\n\t\t<div class=\"header-holder has-default-focus\">\n\t\t\t\n\t\t<a\n\t\t\thref=\"#main\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to main content\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#side-doc-outline\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to in-page navigation\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#\"\n\t\t\tdata-skip-to-ask-learn\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\thidden\n\t\t>\n\t\t\tSkip to Ask Learn chat experience\n\t\t</a>\n\t\n\n\t\t\t<div hidden id=\"cookie-consent-holder\" data-test-id=\"cookie-consent-container\"></div>\n\t\t\t<!-- Unsupported browser warning -->\n\t\t\t<div\n\t\t\t\tid=\"unsupported-browser\"\n\t\t\t\tstyle=\"background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;\"\n\t\t\t\thidden\n\t\t\t>\n\t\t\t\t<div style=\"max-width: 800px; margin: 0 auto;\">\n\t\t\t\t\t<p style=\"font-size: 24px\">This browser is no longer supported.</p>\n\t\t\t\t\t<p style=\"font-size: 16px; margin-top: 16px;\">\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t</p>\n\t\t\t\t\t<div style=\"margin-top: 12px;\">\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://go.microsoft.com/fwlink/p/?LinkID=2092881 \"\n\t\t\t\t\t\t\tstyle=\"background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tDownload Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge\"\n\t\t\t\t\t\t\tstyle=\"background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tMore info about Internet Explorer and Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<!-- site header -->\n\t\t\t<header\n\t\t\t\tid=\"ms--site-header\"\n\t\t\t\tdata-test-id=\"site-header-wrapper\"\n\t\t\t\trole=\"banner\"\n\t\t\t\titemscope=\"itemscope\"\n\t\t\t\titemtype=\"http://schema.org/Organization\"\n\t\t\t>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--mobile-nav\"\n\t\t\t\t\tclass=\"site-header display-none-tablet padding-inline-none gap-none\"\n\t\t\t\t\tdata-bi-name=\"mobile-header\"\n\t\t\t\t\tdata-test-id=\"mobile-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--primary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L1-header\"\n\t\t\t\t\tdata-test-id=\"primary-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--secondary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L2-header\"\n\t\t\t\t\tdata-test-id=\"secondary-header\"\n\t\t\t\t></div>\n\t\t\t</header>\n\t\t\t\n\t\t<!-- banner -->\n\t\t<div data-banner>\n\t\t\t<div id=\"disclaimer-holder\"></div>\n\t\t\t\n\t\t</div>\n\t\t<!-- banner end -->\n\t\n\t\t</div>\n\t</header>\n\t\t\t\t <section\n\t\t\t\t\tid=\"layout-body-menu\"\n\t\t\t\t\tclass=\"layout-body-menu display-flex\"\n\t\t\t\t\tdata-bi-name=\"menu\"\n\t\t\t  >\n\t\t\t\t\t<div\n\t\tid=\"left-container\"\n\t\tclass=\"left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full\"\n\t>\n\t\t<nav\n\t\t\tid=\"affixed-left-container\"\n\t\t\tclass=\"margin-top-sm-tablet position-sticky display-flex flex-direction-column\"\n\t\t\taria-label=\"Primary\"\n\t\t></nav>\n\t</div>\n\t\t\t  </section>\n\n\t\t\t\t<main\n\t\t\t\t\tid=\"main\"\n\t\t\t\t\trole=\"main\"\n\t\t\t\t\tclass=\"layout-body-main \"\n\t\t\t\t\tdata-bi-name=\"content\"\n\t\t\t\t\tlang=\"en-us\"\n\t\t\t\t\tdir=\"ltr\"\n\t\t\t\t>\n\t\t\t\t\t\n\t\t\t<div\n\t\tid=\"ms--content-header\"\n\t\tclass=\"content-header default-focus border-bottom-none\"\n\t\tdata-bi-name=\"content-header\"\n\t>\n\t\t<div class=\"content-header-controls margin-xxs margin-inline-sm-tablet\">\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"contents-button button button-sm margin-right-xxs\"\n\t\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\tdata-contents-button\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-menu\"></span></span>\n\t\t\t\t<span class=\"contents-expand-title\"> Table of contents </span>\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"ap-collapse-behavior ap-expanded button button-sm\"\n\t\t\t\tdata-bi-name=\"ap-collapse\"\n\t\t\t\taria-controls=\"action-panel\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-exit-mode\"></span></span>\n\t\t\t\t<span>Exit editor mode</span>\n\t\t\t</button>\n\t\t</div>\n\t</div>\n\t\t\t<div data-main-column class=\"padding-sm padding-top-none padding-top-sm-tablet\">\n\t\t\t\t<div>\n\t\t\t\t\t\n\t\t<div id=\"article-header\" class=\"background-color-body margin-bottom-xs display-none-print\">\n\t\t\t<div class=\"display-flex align-items-center justify-content-space-between\">\n\t\t\t\t\n\t\t<details\n\t\t\tid=\"article-header-breadcrumbs-overflow-popover\"\n\t\t\tclass=\"popover\"\n\t\t\tdata-for=\"article-header-breadcrumbs\"\n\t\t>\n\t\t\t<summary\n\t\t\t\tclass=\"button button-clear button-primary button-sm inner-focus\"\n\t\t\t\taria-label=\"All breadcrumbs\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-more\"></span>\n\t\t\t\t</span>\n\t\t\t</summary>\n\t\t\t<div id=\"article-header-breadcrumbs-overflow\" class=\"popover-content padding-none\"></div>\n\t\t</details>\n\n\t\t<bread-crumbs\n\t\t\tid=\"article-header-breadcrumbs\"\n\t\t\tdata-test-id=\"article-header-breadcrumbs\"\n\t\t\tclass=\"overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs\"\n\t\t></bread-crumbs>\n\t \n\t\t<div\n\t\t\tid=\"article-header-page-actions\"\n\t\t\tclass=\"opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch\"\n\t\t>\n\t\t\t\n\t\t<button\n\t\t\tclass=\"button button-sm border-none inner-focus display-none-tablet flex-shrink-0 \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-mobile\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-label=\"Ask Learn\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-tablet\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs\t \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-flyout-entry\"\n\t\t\tdata-ask-learn-flyout-entry\n\t\t\tdata-flyout-button=\"toggle\"\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-controls=\"ask-learn-flyout\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tid=\"ms--focus-mode-button\"\n\t\t\tdata-focus-mode\n\t\t\tdata-bi-name=\"focus-mode-entry\"\n\t\t\tclass=\"button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop\"\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-glasses\"></span>\n\t\t\t</span>\n\t\t\t<span>Focus mode</span>\n\t\t</button>\n\t \n\n\t\t\t<details class=\"popover popover-right\" id=\"article-header-page-actions-overflow\">\n\t\t\t\t<summary\n\t\t\t\t\tclass=\"justify-content-flex-start button button-clear button-sm button-primary inner-focus\"\n\t\t\t\t\taria-label=\"More actions\"\n\t\t\t\t\ttitle=\"More actions\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-more-vertical\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<div class=\"popover-content\">\n\t\t\t\t\t\n\t\t<button\n\t\t\tdata-page-action-item=\"overflow-mobile\"\n\t\t\ttype=\"button\"\n\t\t\tclass=\"button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\tdata-contents-button\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\">\n\t\t\t\t<span class=\"docon docon-editor-list-bullet\" aria-hidden=\"true\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"contents-expand-title\">Table of contents</span>\n\t\t</button>\n\t \n\t\t<a\n\t\t\tid=\"lang-link-overflow\"\n\t\t\tclass=\"button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"language-toggle\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-read-in-link\n\t\t\thref=\"#\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\" data-read-in-link-icon>\n\t\t\t\t<span class=\"docon docon-locale-globe\"></span>\n\t\t\t</span>\n\t\t\t<span data-read-in-link-text>Read in English</span>\n\t\t</a>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"collection\"\n\t\t\tdata-bi-name=\"collection\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"collection-status\">Add</span>\n\t\t</button>\n\t\n\t\t\t\t\t\n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"plan\"\n\t\t\tdata-bi-name=\"plan\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"plan-status\">Add to plan</span>\n\t\t</button>\n\t  \n\t\t<a\n\t\t\tdata-contenteditbtn\n\t\t\tclass=\"button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none\"\n\t\t\tdata-bi-name=\"edit\"\n\t\t\t\n\t\t\thref=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\"\n\t\t\tdata-original_content_git_url=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\"\n\t\t\tdata-original_content_git_url_template=\"{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Get-ADGroup.md\"\n\t\t\tdata-pr_repo=\"\"\n\t\t\tdata-pr_branch=\"\"\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-edit-outline\"></span>\n\t\t\t</span>\n\t\t\t<span>Edit</span>\n\t\t</a>\n\t\n\t\t\t\t\t\n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<h4 class=\"font-size-sm padding-left-xxs\">Share via</h4>\n\t\t\n\t\t\t\t\t<a\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook\"\n\t\t\t\t\t\tdata-bi-name=\"facebook\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-facebook-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Facebook</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter\"\n\t\t\t\t\t\tdata-bi-name=\"twitter\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-text\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-xlogo-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>x.com</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin\"\n\t\t\t\t\t\tdata-bi-name=\"linkedin\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-linked-in-logo\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>LinkedIn</span>\n\t\t\t\t\t</a>\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email\"\n\t\t\t\t\t\tdata-bi-name=\"email\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-mail-message\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Email</span>\n\t\t\t\t\t</a>\n\t\t\t  \n\t \n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<button\n\t\t\tclass=\"button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\ttype=\"button\"\n\t\t\tdata-bi-name=\"print\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-popover-close\n\t\t\tdata-print-page\n\t\t\tdata-check-hidden=\"true\"\n\t\t>\n\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-print\"></span>\n\t\t\t</span>\n\t\t\t<span>Print</span>\n\t\t</button>\n\t\n\t\t\t\t</div>\n\t\t\t</details>\n\t\t</div>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<!-- azure disclaimer -->\n\t\t\t\t\t\n\t\t\t\t\t<!-- privateUnauthorizedTemplate is hidden by default -->\n\t\t\t\t\t\n\t\t<div unauthorized-private-section data-bi-name=\"permission-content-unauthorized-private\" hidden>\n\t\t\t<hr class=\"hr margin-top-xs margin-bottom-sm\" />\n\t\t\t<div class=\"notification notification-info\">\n\t\t\t\t<div class=\"notification-content\">\n\t\t\t\t\t<p class=\"margin-top-none notification-title\">\n\t\t\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t\t\t<span class=\"docon docon-exclamation-circle-solid\" aria-hidden=\"true\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Note</span>\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined not-authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-sign-in\" href=\"#\" data-bi-name=\"permission-content-sign-in\">signing in</a> or <a  class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<div class=\"content\"></div>\n\t\t\t\t\t \n\t\t\t\t\t<div class=\"content\"><h1 data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\" class=\"margin-bottom-xs\">Get-ADGroup</h1>\n\n\t<div class=\"margin-block-xxs\">\n\t\t<ul class=\"metadata page-metadata align-items-center\" data-bi-name=\"page info\">\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t</ul>\n\t</div>\n\n<div class=\"metadata\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\">\n\t\t<dl class=\"attributeList\">\n\t\t\t<dt>Module:</dt>\n\t\t\t<dd><a href=\"./?view=windowsserver2025-ps\" data-linktype=\"relative-path\">ActiveDirectory Module</a></dd>\n\t\t</dl>\n</div>\n\n<nav id=\"center-doc-outline\" class=\"doc-outline is-hidden-desktop display-none-print margin-bottom-sm\" data-bi-name=\"intopic toc\" aria-label=\"\">\n  <h2 class=\"title is-6 margin-block-xs\"></h2>\n</nav>\n\n\n\t<div class=\"margin-block-sm\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\">\n\t\t<p>Gets one or more Active Directory groups.</p>\n\n\t</div>\n\n\t<h2 id=\"syntax\" data-chunk-ids=\"filter,identity,ldapfilter\">Syntax</h2>\n\t<h3 id=\"filter\" data-chunk-ids=\"filter\">\n\t\tFilter (Default)\n\t</h3>\n\t<div data-chunk-ids=\"filter\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADGroup\n    -Filter &lt;String&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-ResultPageSize &lt;Int32&gt;]\n    [-ResultSetSize &lt;Int32&gt;]\n    [-SearchBase &lt;String&gt;]\n    [-SearchScope &lt;ADSearchScope&gt;]\n    [-Server &lt;String&gt;]\n    [-ShowMemberTimeToLive]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\t<h3 id=\"identity\" data-chunk-ids=\"identity\">\n\t\tIdentity\n\t</h3>\n\t<div data-chunk-ids=\"identity\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADGroup\n    [-Identity] &lt;ADGroup&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Partition &lt;String&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-Server &lt;String&gt;]\n    [-ShowMemberTimeToLive]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\t<h3 id=\"ldapfilter\" data-chunk-ids=\"ldapfilter\">\n\t\tLdap<wbr>Filter\n\t</h3>\n\t<div data-chunk-ids=\"ldapfilter\">\n\t\t<pre><code class=\"lang-Syntax\">Get-ADGroup\n    -LDAPFilter &lt;String&gt;\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Properties &lt;String[]&gt;]\n    [-ResultPageSize &lt;Int32&gt;]\n    [-ResultSetSize &lt;Int32&gt;]\n    [-SearchBase &lt;String&gt;]\n    [-SearchScope &lt;ADSearchScope&gt;]\n    [-Server &lt;String&gt;]\n    [-ShowMemberTimeToLive]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\n\n\t<h2 id=\"description\" data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\">Description</h2>\n\t<div data-chunk-ids=\"inputs,outputs,filter,identity,ldapfilter,example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results,authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\">\n\t\t<p>The <strong>Get-ADGroup</strong> cmdlet gets a group or performs a search to retrieve multiple groups from an Active Directory.</p>\n<p>The <em>Identity</em> parameter specifies the Active Directory group to get.\nYou can identify a group by its distinguished name (DN), GUID, security identifier (SID), or Security Accounts Manager (SAM) account name.\nYou can also specify group object variable, such as <code>$&lt;localGroupObject&gt;</code>.</p>\n<p>To search for and retrieve more than one group, use the <em>Filter</em> or <em>LDAPFilter</em> parameters.\nThe <em>Filter</em> parameter uses the PowerShell Expression Language to write query strings for Active Directory.\nPowerShell Expression Language syntax provides rich type conversion support for value types received by the <em>Filter</em> parameter.\nFor more information about the <em>Filter</em> parameter syntax, type <code>Get-Help about_ActiveDirectory_Filter</code>.\nIf you have existing Lightweight Directory Access Protocol (LDAP) query strings, you can use the <em>LDAPFilter</em> parameter.</p>\n<p>This cmdlet gets a default set of group object properties.\nTo get additional properties use the <em>Properties</em> parameter.\nFor more information about the how to determine the properties for group objects, see the <em>Properties</em> parameter description.</p>\n\n\t</div>\n\n\t<h2 id=\"examples\" data-chunk-ids=\"example-1-get-a-group-by-sam-account-name,example-2-get-a-group-by-sid,example-3-get-a-group-and-filter-the-results,example-4-get-a-group-from-a-specified-search-base-and-filter-the-results\">Examples</h2>\n\t<h3 id=\"example-1-get-a-group-by-sam-account-name\" data-chunk-ids=\"example-1-get-a-group-by-sam-account-name\">Example 1: Get a group by SAM account name</h3>\n\t<div data-chunk-ids=\"example-1-get-a-group-by-sam-account-name\">\n\t\t<pre><code>PS C:\\&gt; Get-ADGroup -Identity Administrators\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n</code></pre>\n<p>This command gets the group with the SAM account name Administrators.</p>\n\n\t</div>\n\t<h3 id=\"example-2-get-a-group-by-sid\" data-chunk-ids=\"example-2-get-a-group-by-sid\">Example 2: Get a group by SID</h3>\n\t<div data-chunk-ids=\"example-2-get-a-group-by-sid\">\n\t\t<pre><code>PS C:\\&gt; Get-ADGroup -Identity S-1-5-32-544 -Properties member\nDistinguishedName : CN=Administrators,CN=Builtin,DC=Fabrikam,DC=com\nGroupCategory     : Security\nGroupScope        : DomainLocal\nmember            : {CN=Domain Admins,CN=Users,DC=Fabrikam,DC=com, CN=Enterprise Admins,CN=Users,DC=Fabrikam,DC=com, CN=LabAdmin,CN=Users,DC=Fabrikam,DC=com, C\nN=Administrator,CN=Users,DC=Fabrikam,DC=com}\nName              : Administrators\nObjectClass       : group\nObjectGUID        : 02ce3874-dd86-41ba-bddc-013f34019978\nSamAccountName    : Administrators\nSID               : S-1-5-32-544\n</code></pre>\n<p>This command gets the group with SID S-1-5-32-544 and the property member.</p>\n\n\t</div>\n\t<h3 id=\"example-3-get-a-group-and-filter-the-results\" data-chunk-ids=\"example-3-get-a-group-and-filter-the-results\">Example 3: Get a group and filter the results</h3>\n\t<div data-chunk-ids=\"example-3-get-a-group-and-filter-the-results\">\n\t\t<pre><code>PS C:\\&gt; Get-ADGroup -Filter 'GroupCategory -eq \"Security\" -and GroupScope -ne \"DomainLocal\"'\n</code></pre>\n<p>This command gets all groups that have a GroupCategory of Security but do not have a GroupScope of DomainLocal.</p>\n\n\t</div>\n\t<h3 id=\"example-4-get-a-group-from-a-specified-search-base-and-filter-the-results\" data-chunk-ids=\"example-4-get-a-group-from-a-specified-search-base-and-filter-the-results\">Example 4: Get a group from a specified search base and filter the results</h3>\n\t<div data-chunk-ids=\"example-4-get-a-group-from-a-specified-search-base-and-filter-the-results\">\n\t\t<pre><code>PS C:\\&gt; Get-ADGroup -Server localhost:60000 -Filter \"GroupScope -eq 'DomainLocal'\" -SearchBase \"DC=AppNC\"\n\n\nDistinguishedName : CN=AlphaGroup,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : AlphaGroup\nObjectClass       : group\nObjectGUID        : 6498c9fb-7c62-48fe-9972-1461f7f3dec2\nSID               : S-1-*********-*********-**********-**********-**********-*********\n\nDistinguishedName : CN=BranchOffice1,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Security\nGroupScope        : DomainLocal\nName              : BranchOffice1\nObjectClass       : group\nObjectGUID        : 0b7504c5-482b-4a73-88f5-8a76960e4568\nSID               : S-1-*********-*********-**********-**********-**********-**********\n\nDistinguishedName : CN=AccountLeads,OU=AccountDeptOU,DC=AppNC\nGroupCategory     : Distribution\nGroupScope        : DomainLocal\nName              : AccountLeads\nObjectClass       : group\nObjectGUID        : b20c032b-2de9-401a-b48c-341854a37254\nSID               : S-1-*********-*********-**********-**********-**********-*********\n</code></pre>\n<p>This command gets all the DomainLocal groups from the AppNC partition of the AD LDS instance.</p>\n\n\t</div>\n\n\t<h2 id=\"parameters\" data-chunk-ids=\"authtype,credential,filter,identity,ldapfilter,partition,properties,resultpagesize,resultsetsize,searchbase,searchscope,server,showmembertimetolive\">Parameters</h2>\n\t\t<h3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Auth<wbr>Type</h3>\n\t\t<p>Specifies the authentication method to use.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>Negotiate or 0</li>\n<li>Basic or 1</li>\n</ul>\n<p>The default authentication method is Negotiate.</p>\n<p>A Secure Sockets Layer (SSL) connection is required for the Basic authentication method.</p>\n\n\n\t\t<h4 id=\"authtype-properties\" data-chunk-ids=\"authtype\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"authtype\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADAuthType</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>Negotiate, Basic</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"authtype-sets\" data-chunk-ids=\"authtype\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Credential</h3>\n\t\t<p>Specifies the user account credentials to use to perform this task.\nThe default credentials are the credentials of the currently logged on user unless the cmdlet is run from an Active Directory module for Windows PowerShell provider drive.\nIf the cmdlet is run from such a provider drive, the account associated with the drive is the default.</p>\n<p>To specify this parameter, you can type a user name, such as User1 or Domain01\\User01 or you can specify a <strong>PSCredential</strong> object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.</p>\n<p>You can also create a <strong>PSCredential</strong> object by using a script or by using the <strong>Get-Credential</strong> cmdlet.\nYou can then set the <em>Credential</em> parameter to the <strong>PSCredential</strong> object.</p>\n<p>If the acting credentials do not have directory-level permission to perform the task, Active Directory module for Windows PowerShell returns a terminating error.</p>\n\n\n\t\t<h4 id=\"credential-properties\" data-chunk-ids=\"credential\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"credential\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">PSCredential</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"credential-sets\" data-chunk-ids=\"credential\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-filter\" data-chunk-ids=\"filter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Filter</h3>\n\t\t<p>Specifies a query string that retrieves Active Directory objects.\nThis string uses the PowerShell Expression Language syntax.\nThe PowerShell Expression Language syntax provides rich type-conversion support for value types received by the <em>Filter</em> parameter.\nThe syntax uses an in-order representation, which means that the operator is placed between the operand and the value.\nFor more information about the <em>Filter</em> parameter, type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>\n<p>Syntax:</p>\n<p>The following syntax uses Backus-Naur form to show how to use the PowerShell Expression Language for this parameter.</p>\n<p>&lt;filter&gt;  ::= \"{\" &lt;FilterComponentList&gt; \"}\"</p>\n<p>&lt;FilterComponentList&gt; ::= &lt;FilterComponent&gt; | &lt;FilterComponent&gt; &lt;JoinOperator&gt; &lt;FilterComponent&gt; | &lt;NotOperator&gt;  &lt;FilterComponent&gt;</p>\n<p>&lt;FilterComponent&gt; ::= &lt;attr&gt; &lt;FilterOperator&gt; &lt;value&gt; | \"(\" &lt;FilterComponent&gt; \")\"</p>\n<p>&lt;FilterOperator&gt; ::= \"-eq\" | \"-le\" | \"-ge\" | \"-ne\" | \"-lt\" | \"-gt\"| \"-approx\" | \"-bor\" | \"-band\" | \"-recursivematch\" | \"-like\" | \"-notlike\"</p>\n<p>&lt;JoinOperator&gt; ::= \"-and\" | \"-or\"</p>\n<p>&lt;NotOperator&gt; ::= \"-not\"</p>\n<p>&lt;attr&gt; ::= &lt;PropertyName&gt; | &lt;LDAPDisplayName of the attribute&gt;</p>\n<p>&lt;value&gt;::= &lt;compare this value with an &lt;attr&gt; by using the specified &lt;FilterOperator&gt;&gt;</p>\n<p>For a list of supported types for &lt;value&gt;, type <code>Get-Help about_ActiveDirectory_ObjectModel</code>.</p>\n<p>Note: PowerShell wildcards other than *, such as ?, are not supported by the <em>Filter</em> syntax.</p>\n<p>Note: To query using LDAP query strings, use the <em>LDAPFilter</em> parameter.</p>\n\n\n\t\t<h4 id=\"filter-properties\" data-chunk-ids=\"filter\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"filter\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"filter-sets\" data-chunk-ids=\"filter\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"filter\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Identity</h3>\n\t\t<p>Specifies an Active Directory group object by providing one of the following values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>A distinguished name</li>\n<li>A GUID (objectGUID)</li>\n<li>A security identifier (objectSid)</li>\n<li>A security accounts manager account name (sAMAccountName)</li>\n</ul>\n<p>The cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.</p>\n<p>This parameter can also get this object through the pipeline or you can set this parameter to an object instance.</p>\n\n\n\t\t<h4 id=\"identity-properties\" data-chunk-ids=\"identity\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"identity\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADGroup</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"identity-sets\" data-chunk-ids=\"identity\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>0</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-ldapfilter\" data-chunk-ids=\"ldapfilter\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-LDAPFilter</h3>\n\t\t<p>Specifies an LDAP query string that is used to filter Active Directory objects.\nYou can use this parameter to run your existing LDAP queries.\nThe <em>Filter</em> parameter syntax supports the same functionality as the LDAP syntax.\nFor more information, see the <em>Filter</em> parameter description or type <code>Get-Help about_ActiveDirectory_Filter</code>.</p>\n\n\n\t\t<h4 id=\"ldapfilter-properties\" data-chunk-ids=\"ldapfilter\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"ldapfilter\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"ldapfilter-sets\" data-chunk-ids=\"ldapfilter\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"ldapfilter\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-partition\" data-chunk-ids=\"partition\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Partition</h3>\n\t\t<p>Specifies the distinguished name of an Active Directory partition.\nThe distinguished name must be one of the naming contexts on the current directory server.\nThe cmdlet searches this partition to find the object defined by the <em>Identity</em> parameter.</p>\n<p>In many cases, a default value is used for the <em>Partition</em> parameter if no value is specified.\nThe rules for determining the default value are given below.\nNote that rules listed first are evaluated first and once a default value can be determined, no further rules are evaluated.</p>\n<p>In Active Directory Domain Services (AD DS) environments, a default value for <em>Partition</em> is set in the following cases:</p>\n<ul>\n<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>\n<li>If none of the previous cases apply, the default value of <em>Partition</em> is set to the default partition or naming context of the target domain.</li>\n</ul>\n<p>In Active Directory Lightweight Directory Services (AD LDS) environments, a default value for <em>Partition</em> is set in the following cases:</p>\n<ul>\n<li>If the <em>Identity</em> parameter is set to a distinguished name, the default value of <em>Partition</em> is automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <em>Partition</em> is automatically generated from the current path in the drive.</li>\n<li>If the target AD LDS instance has a default naming context, the default value of <em>Partition</em> will be set to the default naming context.\nTo specify a default naming context for an AD LDS environment, set the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent object (<strong>nTDSDSA</strong>) for the AD LDS instance.</li>\n<li>If none of the previous cases apply, the <em>Partition</em> parameter does not take any default value.</li>\n</ul>\n\n\n\t\t<h4 id=\"partition-properties\" data-chunk-ids=\"partition\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"partition\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"partition-sets\" data-chunk-ids=\"partition\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"partition\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-properties\" data-chunk-ids=\"properties\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Properties</h3>\n\t\t<p>Specifies the properties of the output object to retrieve from the server.\nUse this parameter to retrieve properties that are not included in the default set.</p>\n<p>Specify properties for this parameter as a comma-separated list of names.\nTo display all of the attributes that are set on the object, specify * (asterisk).</p>\n<p>To specify an individual extended property, use the name of the property.\nFor properties that are not default or extended properties, you must specify the LDAP display name of the attribute.</p>\n<p>To retrieve properties and display them for an object, you can use the Get-* cmdlet associated with the object and pass the output to the <strong>Get-Member</strong> cmdlet.</p>\n\n\n\t\t<h4 id=\"properties-properties\" data-chunk-ids=\"properties\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"properties\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><p><span class=\"no-loc xref\">String</span><span>[</span><span>]</span></p>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Aliases:</td><td>Property</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"properties-sets\" data-chunk-ids=\"properties\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"properties\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-resultpagesize\" data-chunk-ids=\"resultpagesize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Result<wbr>Page<wbr>Size</h3>\n\t\t<p>Specifies the number of objects to include in one page for an AD DS query.</p>\n<p>The default is 256 objects per page.</p>\n\n\n\t\t<h4 id=\"resultpagesize-properties\" data-chunk-ids=\"resultpagesize\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"resultpagesize\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Int32</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"resultpagesize-sets\" data-chunk-ids=\"resultpagesize\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultpagesize\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-resultsetsize\" data-chunk-ids=\"resultsetsize\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Result<wbr>Set<wbr>Size</h3>\n\t\t<p>Specifies the maximum number of objects to return for an AD DS query.\nIf you want to receive all of the objects, set this parameter to $Null (null value).\nYou can use Ctrl+C to stop the query and return of objects.</p>\n<p>The default is $Null.</p>\n\n\n\t\t<h4 id=\"resultsetsize-properties\" data-chunk-ids=\"resultsetsize\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"resultsetsize\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Int32</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"resultsetsize-sets\" data-chunk-ids=\"resultsetsize\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"resultsetsize\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-searchbase\" data-chunk-ids=\"searchbase\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Search<wbr>Base</h3>\n\t\t<p>Specifies an Active Directory path to search under.</p>\n<p>When you run a cmdlet from an Active Directory provider drive, the default value of this parameter is the current path of the drive.</p>\n<p>When you run a cmdlet outside of an Active Directory provider drive against an AD DS target, the default value of this parameter is the default naming context of the target domain.</p>\n<p>When you run a cmdlet outside of an Active Directory provider drive against an AD LDS target, the default value is the default naming context of the target LDS instance if one has been specified by setting the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent object (<strong>nTDSDSA</strong>) for the AD LDS instance.\nIf no default naming context has been specified for the target AD LDS instance, then this parameter has no default value.</p>\n<p>When the value of the <em>SearchBase</em> parameter is set to an empty string and you are connected to a GC port, all partitions are searched.\nIf the value of the <em>SearchBase</em> parameter is set to an empty string and you are not connected to a GC port, an error is thrown.</p>\n\n\n\t\t<h4 id=\"searchbase-properties\" data-chunk-ids=\"searchbase\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"searchbase\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"searchbase-sets\" data-chunk-ids=\"searchbase\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchbase\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchbase\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-searchscope\" data-chunk-ids=\"searchscope\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Search<wbr>Scope</h3>\n\t\t<p>Specifies the scope of an Active Directory search.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>Base or 0</li>\n<li>OneLevel or 1</li>\n<li>Subtree or 2</li>\n</ul>\n<p>A Base query searches only the current path or object.\nA OneLevel query searches the immediate children of that path or object.\nA Subtree query searches the current path or object and all children of that path or object.</p>\n\n\n\t\t<h4 id=\"searchscope-properties\" data-chunk-ids=\"searchscope\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"searchscope\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADSearchScope</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>Base, OneLevel, Subtree</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"searchscope-sets\" data-chunk-ids=\"searchscope\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchscope\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tFilter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"searchscope\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tLdap<wbr>Filter \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Server</h3>\n\t\t<p>Specifies the Active Directory Domain Services instance to connect to, by providing one of the following values for a corresponding domain name or directory server.\nThe service may be any of the following: Active Directory Lightweight Domain Services, Active Directory Domain Services or Active Directory Snapshot instance.</p>\n<p>Specify the Active Directory Domain Services instance in one of the following ways:</p>\n<p>Domain name values:</p>\n<ul>\n<li>Fully qualified domain name</li>\n<li>NetBIOS name</li>\n</ul>\n<p>Directory server values:</p>\n<ul>\n<li>Fully qualified directory server name</li>\n<li>NetBIOS name</li>\n<li>Fully qualified directory server name and port</li>\n</ul>\n<p>The default value for this parameter is determined by one of the following methods in the order that they are listed:</p>\n<ul>\n<li>By using the <em>Server</em> value from objects passed through the pipeline</li>\n<li>By using the server information associated with the Active Directory Domain Services Windows PowerShell provider drive, when the cmdlet runs in that drive</li>\n<li>By using the domain of the computer running Windows PowerShell</li>\n</ul>\n\n\n\t\t<h4 id=\"server-properties\" data-chunk-ids=\"server\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"server\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"server-sets\" data-chunk-ids=\"server\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-showmembertimetolive\" data-chunk-ids=\"showmembertimetolive\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Show<wbr>Member<wbr>Time<wbr>ToLive</h3>\n\t\t<p>Indicates that this cmdlet displays Time to Live (TTL) values for group members.</p>\n\n\n\t\t<h4 id=\"showmembertimetolive-properties\" data-chunk-ids=\"showmembertimetolive\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"showmembertimetolive\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">SwitchParameter</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"showmembertimetolive-sets\" data-chunk-ids=\"showmembertimetolive\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"showmembertimetolive\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"common-parameters\" data-no-chunk=\"\">CommonParameters</h3>\n\t\t<div data-no-chunk=\"\">\n\t\t\t<p>This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n<a href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\">about_CommonParameters</a>.</p>\n\n\t\t</div>\n\n\t<h2 id=\"inputs\" data-chunk-ids=\"inputs\">Inputs</h2>\n\t\t\t<h3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">None or Microsoft.ActiveDirectory.Management.ADGroup</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"inputs\">\n\t\t\t\t<p>A group object is received by the <em>Identity</em> parameter.</p>\n\n\t\t\t</div>\n\n\t<h2 id=\"outputs\" data-chunk-ids=\"outputs\">Outputs</h2>\n\t\t\t<h3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">Microsoft.ActiveDirectory.Management.ADGroup</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"outputs\">\n\t\t\t\t<p>Returns one or more group objects.</p>\n<p>The <strong>Get-ADGroup</strong> cmdlet returns a default set of <strong>ADGroup</strong> property values.\nTo retrieve additional <strong>ADGroup</strong> properties, use the <em>Properties</em> parameter.</p>\n<p>To view the properties for an <strong>ADGroup</strong> object, see the following examples.\nTo run these examples, replace &lt;group&gt; with a group identifier such as Administrators.</p>\n<p>To get a list of the default set of properties of an <strong>ADGroup</strong> object, use the following command:</p>\n<p><code>Get-ADGroup</code>&lt;group&gt;<code>| Get-Member</code></p>\n<p>To get a list of all the properties of an <strong>ADGroup</strong> object, use the following command:</p>\n<p><code>Get-ADGroup</code>&lt;group&gt;<code>-Properties * | Get-Member</code></p>\n\n\t\t\t</div>\n\n\n\t<h2 id=\"related-links\" data-no-chunk=\"\">Related Links</h2>\n\t<ul data-no-chunk=\"\">\n\t\t\t<li><a href=\"new-adgroup?view=windowsserver2025-ps\" data-linktype=\"relative-path\">New-ADGroup</a></li>\n\t\t\t<li><a href=\"remove-adgroup?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Remove-ADGroup</a></li>\n\t\t\t<li><a href=\"set-adgroup?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Set-ADGroup</a></li>\n\t</ul>\n</div>\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t></div>\n\t \n\t\t<div\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\t<div\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\n\t\t\t\t\t\n\t\t<!-- feedback section -->\n\t\t<section\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t>\n\t\t\t<hr class=\"hr\" />\n\t\t\t<h2 id=\"ms--feedback\" class=\"title is-3\">Feedback</h2>\n\t\t\t<div class=\"display-flex flex-wrap-wrap align-items-center\">\n\t\t\t\t<p class=\"font-weight-semibold margin-xxs margin-left-none\">\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t</p>\n\t\t\t\t<div class=\"buttons\">\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Yes</span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>No</span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</section>\n\t\t<!-- end feedback section -->\n\t\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t</div>\n\t\t\t\n\t\t<div\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t></div>\n\t\n\t\t\n\t\t\t\t</main>\n\t\t\t\t<aside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  >\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t>\n\t\t\t<div id=\"affixed-right-container\" data-bi-name=\"right-column\">\n\t\t\t\t\n\t\t<nav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t>\n\t\t\t<h3>In this article</h3>\n\t\t</nav>\n\t\n\t\t\t\t<!-- Feedback -->\n\t\t\t\t\n\t\t<section\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t>\n\t\t\t<p class=\"font-weight-semibold margin-bottom-xs\">Was this page helpful?</p>\n\t\t\t<div class=\"buttons\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>Yes</span>\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>No</span>\n\t\t\t\t</button>\n\t\t\t</div>\n\t\t</section>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t  </aside> <section\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  >\n\t\t\t\t\t <div\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n></div>\n\t\t\t  </section> <div class=\"layout-body-footer \" data-bi-name=\"layout-footer\">\n\t\t<footer\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t>\n\t\t\t<div class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\">\n\t\t\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t><span class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t><span class=\"docon docon-world\"></span></span\n\t\t\t><span class=\"local-selector-link-text\">en-us</span></a\n\t\t>\n\t\n\t\t\t\t<div class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>\n\t\t<svg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t>\n\t\t\t<path\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t></path>\n\t\t</svg>\n\t\n\t\t\t<span>Your Privacy Choices</span></a\n\t\t>\n\t\n\t</div>\n\t\t\t\t<div class=\"flex-shrink-0\">\n\t\t<div class=\"dropdown has-caret-up\">\n\t\t\t<button\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-sun\" aria-hidden=\"true\"></span>\n\t\t\t\t</span>\n\t\t\t\t<span>Theme</span>\n\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t</span>\n\t\t\t</button>\n\t\t\t<div class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\">\n\t\t\t\t<ul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\">\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-light margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Light </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-dark margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Dark </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-high-contrast margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> High contrast </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>\n\t</div>\n\t\t\t</div>\n\t\t\t<ul class=\"links\" data-bi-name=\"footerlinks\">\n\t\t\t\t<li class=\"manage-cookies-holder\" hidden=\"\"></li>\n\t\t\t\t<li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>AI Disclaimer</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Previous Versions</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Blog</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Contribute</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Privacy</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Terms of Use</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Trademarks</a\n\t\t>\n\t\n\t</li>\n\t\t\t\t<li>&copy; Microsoft 2025</li>\n\t\t\t</ul>\n\t\t</footer>\n\t</footer>\n\t\t\t</body>\n\t\t</html>"}, {"RelevanceScore": 1, "CreatedAt": "2025-07-28T10:10:17Z", "PatternType": "common_mistake", "Id": "pattern_user_management_14d7da80", "Operation": "update", "UpdatedAt": "2025-07-28T10:10:17Z", "BestPractices": [], "Title": "Microsoft Docs: Set-ADUser", "CodeTemplate": "$&lt;localUserObject&gt;", "CommonMistakes": [], "Domain": "user_management", "RequiredParameters": "[\"Get-ADUser\\u003c/strong\\u003e cmdlet to retrieve a user object and then pass the object through the pipeline to the \\u003cstrong\\u003eSet-ADUser\\u003c/strong\\u003e cmdlet.\\u003c/p\\u003e\",\"Get-ADUser\\u003c/strong\\u003e object.\",\"Get-ADUser -Filter \\u0027Name -like \\\"*\\\"\\u0027 -SearchBase \\u0027OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM\\u0027 -Properties DisplayName | % {Set-ADUser $_ -DisplayName ($_.Surname + \\u0027 \\u0027 + $_.GivenName)}\",\"Get-ADUser -Identity GlenJohn -Properties mail,department\",\"Get-ADUser -Identity GlenJohn -Server Corp-DC01\",\"Get-ADUser -Identity \\\"DavidChew\\\" | Set-ADUser -Manager \\\"ElisaDaugherty\\\"\",\"Get-ADUser\\u003c/strong\\u003e cmdlet to get the user <PERSON><PERSON><PERSON><PERSON>, and then passes the object to the current cmdlet by using the pipeline operator.\\u003c/p\\u003e\",\"Get-ADUser\\u003c/code\\u003e cmdlet. When you specify the \\u003cstrong\\u003eInstance\\u003c/strong\\u003e parameter, you cannot specify\",\"Get-ADUser\\u003c/code\\u003e cmdlet and then modified is received by\",\"get-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eGet-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"Set-ADUser (ActiveDirectory) | Microsoft Learn\\u003c/title\\u003e\",\"set-aduser?view=windowsserver2025-ps\\\" /\\u003e\",\"Set-ADUser (ActiveDirectory)\\\" /\\u003e\",\"Set-ADUser\\\" /\\u003e\",\"set-aduser?view=windowsserver2025-ps\\u0026amp;wt.mc_id=ps-gethelp\\\" /\\u003e\",\"Set-ADUser.md\\\" /\\u003e\",\"set-aduser\\\" /\\u003e\",\"Set-ADUser.md\\\"\",\"Set-ADUser\\u003c/h1\\u003e\",\"Set-ADUser\",\"Set-ADUser\\u003c/code\\u003e cmdlet modifies the properties of an Active Directory user. You can modify\",\"Set-ADUser\\u003c/strong\\u003e cmdlet.\\u003c/p\\u003e\",\"Set-ADUser\\u003c/strong\\u003e cmdlet makes the same changes to the original user object.\",\"Set-ADUser @params\",\"Set-ADUser $_ -DisplayName ($_.Surname + \\u0027 \\u0027 + $_.GivenName)}\",\"Set-ADUser -Identity GlenJohn -Replace @{title=\\\"director\\\";mail=\\\"<EMAIL>\\\"}\",\"Set-ADUser -Identity GlenJohn -Remove @{otherMailbox=\\\"glen.john\\\"} -Add @{url=\\\"fabrikam.com\\\"} -Replace @{title=\\\"manager\\\"} -Clear description\",\"Set-ADUser -Instance $User\",\"Set-ADUser -Identity \\\"SarahDavis\\\" -Replace $ReplaceHashTable\",\"Set-ADUser -Identity ChewDavid -Manager $Manager -Server Branch-DC02\",\"Set-ADUser -Manager \\\"ElisaDaugherty\\\"\",\"set-adaccountcontrol?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eSet-ADAccountControl\\u003c/a\\u003e\\u003c/li\\u003e\",\"New-ADUser\\u003c/strong\\u003e cmdlet are disabled if no password is provided.\\u003c/p\\u003e\",\"new-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eNew-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"remove-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\",\"move-aduser?view=windowsserver2025-ps\\\" data-linktype=\\\"relative-path\\\"\\u003eRemove-ADUser\\u003c/a\\u003e\\u003c/li\\u003e\"]", "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "user_management", "update"], "Abstract": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Set-ADUser (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" content...", "Sources": [{"SourceType": "microsoft_docs", "CredibilityScore": 0.95, "PublishedAt": "2025-07-28T10:10:17Z", "Url": "https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-aduser", "ScrapedAt": "2025-07-28T10:10:17Z", "Id": "dba55ee6-9cd1-4082-8415-d9fe73c76705", "Author": "Microsoft", "Title": "Microsoft Docs: Set-ADUser"}], "Content": " <!DOCTYPE html>\n\t\t<html\n\t\t\tclass=\"layout layout-holy-grail   show-table-of-contents reference show-breadcrumb default-focus\"\n\t\t\tlang=\"en-us\"\n\t\t\tdir=\"ltr\"\n\t\t\tdata-authenticated=\"false\"\n\t\t\tdata-auth-status-determined=\"false\"\n\t\t\tdata-target=\"docs\"\n\t\t\tx-ms-format-detection=\"none\"\n\t\t>\n\t\t\t\n\t\t<head>\n\t\t\t<title>Set-ADUser (ActiveDirectory) | Microsoft Learn</title>\n\t\t\t<meta charset=\"utf-8\" />\n\t\t\t<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n\t\t\t<meta name=\"color-scheme\" content=\"light dark\" />\n\n\t\t\t<meta name=\"description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<link rel=\"canonical\" href=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-aduser?view=windowsserver2025-ps\" /> \n\n\t\t\t<!-- Non-customizable open graph and sharing-related metadata -->\n\t\t\t<meta name=\"twitter:card\" content=\"summary_large_image\" />\n\t\t\t<meta name=\"twitter:site\" content=\"@MicrosoftLearn\" />\n\t\t\t<meta property=\"og:type\" content=\"website\" />\n\t\t\t<meta property=\"og:image:alt\" content=\"Microsoft Learn\" />\n\t\t\t<meta property=\"og:image\" content=\"https://learn.microsoft.com/en-us/media/open-graph-image.png\" />\n\t\t\t<!-- Page specific open graph and sharing-related metadata -->\n\t\t\t<meta property=\"og:title\" content=\"Set-ADUser (ActiveDirectory)\" />\n\t\t\t<meta property=\"og:url\" content=\"https://learn.microsoft.com/en-us/powershell/module/activedirectory/set-aduser?view=windowsserver2025-ps\" />\n\t\t\t<meta property=\"og:description\" content=\"Use this topic to help manage Windows and Windows Server technologies with Windows PowerShell.\" />\n\t\t\t<meta name=\"platform_id\" content=\"6177643c-2383-70f9-f971-3135e681fad3\" /> \n\t\t\t<meta name=\"locale\" content=\"en-us\" />\n\t\t\t <meta name=\"adobe-target\" content=\"true\" />\n\t\t\t<meta name=\"uhfHeaderId\" content=\"MSDocsHeader-M365-IT\" />\n\n\t\t\t<meta name=\"page_type\" content=\"powershell\" />\n\n\t\t\t<!--page specific meta tags-->\n\t\t\t\n\n\t\t\t<!-- custom meta tags -->\n\t\t\t\n\t\t<meta name=\"uid\" content=\"ActiveDirectory.Set-ADUser\" />\n\t\n\t\t<meta name=\"module\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"schema\" content=\"PowerShellCmdlet1\" />\n\t\n\t\t<meta name=\"ROBOTS\" content=\"INDEX, FOLLOW\" />\n\t\n\t\t<meta name=\"apiPlatform\" content=\"powershell\" />\n\t\n\t\t<meta name=\"archive_url\" content=\"https://learn.microsoft.com/previous-versions/powershell/windows/get-started\" />\n\t\n\t\t<meta name=\"author\" content=\"robinharwood\" />\n\t\n\t\t<meta name=\"breadcrumb_path\" content=\"/powershell/windows/bread/toc.json\" />\n\t\n\t\t<meta name=\"feedback_product_url\" content=\"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\" />\n\t\n\t\t<meta name=\"feedback_system\" content=\"Standard\" />\n\t\n\t\t<meta name=\"manager\" content=\"tedhudek\" />\n\t\n\t\t<meta name=\"ms.author\" content=\"roharwoo\" />\n\t\n\t\t<meta name=\"ms.devlang\" content=\"powershell\" />\n\t\n\t\t<meta name=\"ms.service\" content=\"windows-11\" />\n\t\n\t\t<meta name=\"ms.topic\" content=\"reference\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56936876-97d9-45cc-ad1b-9d63320447c8\" />\n\t\n\t\t<meta name=\"products\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/56754133-c3c3-4a9f-af19-71bdbe19fccf\" />\n\t\n\t\t<meta name=\"document type\" content=\"cmdlet\" />\n\t\n\t\t<meta name=\"external help file\" content=\"Microsoft.ActiveDirectory.Management.dll-Help.xml\" />\n\t\n\t\t<meta name=\"HelpUri\" content=\"https://learn.microsoft.com/powershell/module/activedirectory/set-aduser?view=windowsserver2025-ps&amp;wt.mc_id=ps-gethelp\" />\n\t\n\t\t<meta name=\"Module Name\" content=\"ActiveDirectory\" />\n\t\n\t\t<meta name=\"ms.date\" content=\"2016-12-27T00:00:00Z\" />\n\t\n\t\t<meta name=\"PlatyPS schema version\" content=\"2024-05-01T00:00:00Z\" />\n\t\n\t\t<meta name=\"document_id\" content=\"79e36514-0e8b-272e-706c-351732b27180\" />\n\t\n\t\t<meta name=\"document_version_independent_id\" content=\"e9dbeab9-f586-16f8-abfd-cd3d6b7a9d01\" />\n\t\n\t\t<meta name=\"updated_at\" content=\"2025-05-14T22:44:00Z\" />\n\t\n\t\t<meta name=\"original_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\" />\n\t\n\t\t<meta name=\"gitcommit\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/0ef3f225d29e26d1cf3119f37dfff70bb6165746/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\" />\n\t\n\t\t<meta name=\"git_commit_id\" content=\"0ef3f225d29e26d1cf3119f37dfff70bb6165746\" />\n\t\n\t\t<meta name=\"monikers\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"default_moniker\" content=\"windowsserver2025-ps\" />\n\t\n\t\t<meta name=\"site_name\" content=\"Docs\" />\n\t\n\t\t<meta name=\"depot_name\" content=\"TechNet.windows-powershell\" />\n\t\n\t\t<meta name=\"in_right_rail\" content=\"h2h3\" />\n\t\n\t\t<meta name=\"page_kind\" content=\"command\" />\n\t\n\t\t<meta name=\"toc_rel\" content=\"../windowsserver2025-ps/toc.json\" />\n\t\n\t\t<meta name=\"feedback_help_link_type\" content=\"\" />\n\t\n\t\t<meta name=\"feedback_help_link_url\" content=\"\" />\n\t\n\t\t<meta name=\"config_moniker_range\" content=\"WindowsServer2025-ps\" />\n\t\n\t\t<meta name=\"asset_id\" content=\"module/activedirectory/set-aduser\" />\n\t\n\t\t<meta name=\"moniker_range_name\" content=\"ffb05b7b47577225af7c7b6a20151268\" />\n\t\n\t\t<meta name=\"item_type\" content=\"Content\" />\n\t\n\t\t<meta name=\"source_path\" content=\"docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\" />\n\t\n\t\t<meta name=\"github_feedback_content_git_url\" content=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\" />\n\t \n\t\t<meta name=\"spProducts\" content=\"https://authoring-docs-microsoft.poolparty.biz/devrel/43b2e5aa-8a6d-4de2-a252-692232e5edc8\" data-source=\"generated\" />\n\t\n\n\t\t\t<!-- assets and js globals -->\n\t\t\t\n\t\t\t<link rel=\"stylesheet\" href=\"/static/assets/0.4.03126.7002-3880ccdd/styles/site-ltr.css\" />\n\t\t\t<link rel=\"preconnect\" href=\"//mscom.demdex.net\" crossorigin />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//target.microsoft.com\" />\n\t\t\t\t\t\t<link rel=\"dns-prefetch\" href=\"//microsoftmscompoc.tt.omtrdc.net\" />\n\t\t\t\t\t\t<link\n\t\t\t\t\t\t\trel=\"preload\"\n\t\t\t\t\t\t\tas=\"script\"\n\t\t\t\t\t\t\thref=\"/static/third-party/adobe-target/at-js/2.9.0/at.js\"\n\t\t\t\t\t\t\tintegrity=\"sha384-1/viVM50hgc33O2gOgkWz3EjiD/Fy/ld1dKYXJRUyjNYVEjSUGcSN+iPiQF7e4cu\"\n\t\t\t\t\t\t\tcrossorigin=\"anonymous\"\n\t\t\t\t\t\t\tid=\"adobe-target-script\"\n\t\t\t\t\t\t\ttype=\"application/javascript\"\n\t\t\t\t\t\t/>\n\t\t\t<script src=\"https://wcpstatic.microsoft.com/mscc/lib/v2/wcp-consent.js\"></script>\n\t\t\t<script src=\"https://js.monitor.azure.com/scripts/c/ms.jsll-4.min.js\"></script>\n\t\t\t<script src=\"/_themes/docs.theme/master/en-us/_themes/global/deprecation.js\"></script>\n\n\t\t\t<!-- msdocs global object -->\n\t\t\t<script id=\"msdocs-script\">\n\t\tvar msDocs = {\n  \"environment\": {\n    \"accessLevel\": \"online\",\n    \"azurePortalHostname\": \"portal.azure.com\",\n    \"reviewFeatures\": false,\n    \"supportLevel\": \"production\",\n    \"systemContent\": true,\n    \"siteName\": \"learn\",\n    \"legacyHosting\": false\n  },\n  \"data\": {\n    \"contentLocale\": \"en-us\",\n    \"contentDir\": \"ltr\",\n    \"userLocale\": \"en-us\",\n    \"userDir\": \"ltr\",\n    \"pageTemplate\": \"Reference\",\n    \"brand\": \"\",\n    \"context\": {},\n    \"standardFeedback\": true,\n    \"showFeedbackReport\": false,\n    \"feedbackHelpLinkType\": \"\",\n    \"feedbackHelpLinkUrl\": \"\",\n    \"feedbackSystem\": \"Standard\",\n    \"feedbackGitHubRepo\": \"\",\n    \"feedbackProductUrl\": \"https://support.microsoft.com/windows/send-feedback-to-microsoft-with-the-feedback-hub-app-f59187f8-8739-22d6-ba93-f66612949332\",\n    \"extendBreadcrumb\": true,\n    \"isEditDisplayable\": true,\n    \"isPrivateUnauthorized\": false,\n    \"hideViewSource\": false,\n    \"isPermissioned\": false,\n    \"hasRecommendations\": false,\n    \"contributors\": []\n  },\n  \"functions\": {}\n};;\n\t</script>\n\n\t\t\t<!-- base scripts, msdocs global should be before this -->\n\t\t\t<script src=\"/static/assets/0.4.03126.7002-3880ccdd/scripts/en-us/index-docs.js\"></script>\n\t\t\t\n\n\t\t\t<!-- json-ld -->\n\t\t\t\n\t\t</head>\n\t\n\t\t\t<body\n\t\t\t\tid=\"body\"\n\t\t\t\tdata-bi-name=\"body\"\n\t\t\t\tclass=\"layout-body \"\n\t\t\t\tlang=\"en-us\"\n\t\t\t\tdir=\"ltr\"\n\t\t\t>\n\t\t\t\t<header class=\"layout-body-header\">\n\t\t<div class=\"header-holder has-default-focus\">\n\t\t\t\n\t\t<a\n\t\t\thref=\"#main\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to main content\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#side-doc-outline\"\n\t\t\t\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\t\n\t\t>\n\t\t\tSkip to in-page navigation\n\t\t</a>\n\t\n\t\t<a\n\t\t\thref=\"#\"\n\t\t\tdata-skip-to-ask-learn\n\t\t\tstyle=\"z-index: 1070\"\n\t\t\tclass=\"outline-color-text visually-hidden-until-focused position-fixed inner-focus focus-visible top-0 left-0 right-0 padding-xs text-align-center background-color-body\"\n\t\t\thidden\n\t\t>\n\t\t\tSkip to Ask Learn chat experience\n\t\t</a>\n\t\n\n\t\t\t<div hidden id=\"cookie-consent-holder\" data-test-id=\"cookie-consent-container\"></div>\n\t\t\t<!-- Unsupported browser warning -->\n\t\t\t<div\n\t\t\t\tid=\"unsupported-browser\"\n\t\t\t\tstyle=\"background-color: white; color: black; padding: 16px; border-bottom: 1px solid grey;\"\n\t\t\t\thidden\n\t\t\t>\n\t\t\t\t<div style=\"max-width: 800px; margin: 0 auto;\">\n\t\t\t\t\t<p style=\"font-size: 24px\">This browser is no longer supported.</p>\n\t\t\t\t\t<p style=\"font-size: 16px; margin-top: 16px;\">\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t</p>\n\t\t\t\t\t<div style=\"margin-top: 12px;\">\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://go.microsoft.com/fwlink/p/?LinkID=2092881 \"\n\t\t\t\t\t\t\tstyle=\"background-color: #0078d4; border: 1px solid #0078d4; color: white; padding: 6px 12px; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tDownload Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://learn.microsoft.com/en-us/lifecycle/faq/internet-explorer-microsoft-edge\"\n\t\t\t\t\t\t\tstyle=\"background-color: white; padding: 6px 12px; border: 1px solid #505050; color: #171717; border-radius: 2px; display: inline-block;\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tMore info about Internet Explorer and Microsoft Edge\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<!-- site header -->\n\t\t\t<header\n\t\t\t\tid=\"ms--site-header\"\n\t\t\t\tdata-test-id=\"site-header-wrapper\"\n\t\t\t\trole=\"banner\"\n\t\t\t\titemscope=\"itemscope\"\n\t\t\t\titemtype=\"http://schema.org/Organization\"\n\t\t\t>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--mobile-nav\"\n\t\t\t\t\tclass=\"site-header display-none-tablet padding-inline-none gap-none\"\n\t\t\t\t\tdata-bi-name=\"mobile-header\"\n\t\t\t\t\tdata-test-id=\"mobile-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--primary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L1-header\"\n\t\t\t\t\tdata-test-id=\"primary-header\"\n\t\t\t\t></div>\n\t\t\t\t<div\n\t\t\t\t\tid=\"ms--secondary-nav\"\n\t\t\t\t\tclass=\"site-header display-none display-flex-tablet\"\n\t\t\t\t\tdata-bi-name=\"L2-header\"\n\t\t\t\t\tdata-test-id=\"secondary-header\"\n\t\t\t\t></div>\n\t\t\t</header>\n\t\t\t\n\t\t<!-- banner -->\n\t\t<div data-banner>\n\t\t\t<div id=\"disclaimer-holder\"></div>\n\t\t\t\n\t\t</div>\n\t\t<!-- banner end -->\n\t\n\t\t</div>\n\t</header>\n\t\t\t\t <section\n\t\t\t\t\tid=\"layout-body-menu\"\n\t\t\t\t\tclass=\"layout-body-menu display-flex\"\n\t\t\t\t\tdata-bi-name=\"menu\"\n\t\t\t  >\n\t\t\t\t\t<div\n\t\tid=\"left-container\"\n\t\tclass=\"left-container display-none display-block-tablet padding-inline-sm padding-bottom-sm width-full\"\n\t>\n\t\t<nav\n\t\t\tid=\"affixed-left-container\"\n\t\t\tclass=\"margin-top-sm-tablet position-sticky display-flex flex-direction-column\"\n\t\t\taria-label=\"Primary\"\n\t\t></nav>\n\t</div>\n\t\t\t  </section>\n\n\t\t\t\t<main\n\t\t\t\t\tid=\"main\"\n\t\t\t\t\trole=\"main\"\n\t\t\t\t\tclass=\"layout-body-main \"\n\t\t\t\t\tdata-bi-name=\"content\"\n\t\t\t\t\tlang=\"en-us\"\n\t\t\t\t\tdir=\"ltr\"\n\t\t\t\t>\n\t\t\t\t\t\n\t\t\t<div\n\t\tid=\"ms--content-header\"\n\t\tclass=\"content-header default-focus border-bottom-none\"\n\t\tdata-bi-name=\"content-header\"\n\t>\n\t\t<div class=\"content-header-controls margin-xxs margin-inline-sm-tablet\">\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"contents-button button button-sm margin-right-xxs\"\n\t\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\t\taria-haspopup=\"true\"\n\t\t\t\tdata-contents-button\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-menu\"></span></span>\n\t\t\t\t<span class=\"contents-expand-title\"> Table of contents </span>\n\t\t\t</button>\n\t\t\t<button\n\t\t\t\ttype=\"button\"\n\t\t\t\tclass=\"ap-collapse-behavior ap-expanded button button-sm\"\n\t\t\t\tdata-bi-name=\"ap-collapse\"\n\t\t\t\taria-controls=\"action-panel\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\" aria-hidden=\"true\"><span class=\"docon docon-exit-mode\"></span></span>\n\t\t\t\t<span>Exit editor mode</span>\n\t\t\t</button>\n\t\t</div>\n\t</div>\n\t\t\t<div data-main-column class=\"padding-sm padding-top-none padding-top-sm-tablet\">\n\t\t\t\t<div>\n\t\t\t\t\t\n\t\t<div id=\"article-header\" class=\"background-color-body margin-bottom-xs display-none-print\">\n\t\t\t<div class=\"display-flex align-items-center justify-content-space-between\">\n\t\t\t\t\n\t\t<details\n\t\t\tid=\"article-header-breadcrumbs-overflow-popover\"\n\t\t\tclass=\"popover\"\n\t\t\tdata-for=\"article-header-breadcrumbs\"\n\t\t>\n\t\t\t<summary\n\t\t\t\tclass=\"button button-clear button-primary button-sm inner-focus\"\n\t\t\t\taria-label=\"All breadcrumbs\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-more\"></span>\n\t\t\t\t</span>\n\t\t\t</summary>\n\t\t\t<div id=\"article-header-breadcrumbs-overflow\" class=\"popover-content padding-none\"></div>\n\t\t</details>\n\n\t\t<bread-crumbs\n\t\t\tid=\"article-header-breadcrumbs\"\n\t\t\tdata-test-id=\"article-header-breadcrumbs\"\n\t\t\tclass=\"overflow-hidden flex-grow-1 margin-right-sm margin-right-md-tablet margin-right-lg-desktop margin-left-negative-xxs padding-left-xxs\"\n\t\t></bread-crumbs>\n\t \n\t\t<div\n\t\t\tid=\"article-header-page-actions\"\n\t\t\tclass=\"opacity-none margin-left-auto display-flex flex-wrap-no-wrap align-items-stretch\"\n\t\t>\n\t\t\t\n\t\t<button\n\t\t\tclass=\"button button-sm border-none inner-focus display-none-tablet flex-shrink-0 \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-mobile\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-label=\"Ask Learn\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none display-inline-flex-tablet display-none-desktop flex-shrink-0 margin-right-xxs \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-modal-entry-tablet\"\n\t\t\tdata-ask-learn-modal-entry\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t\t<button\n\t\t\tclass=\"button button-sm display-none flex-shrink-0 display-inline-flex-desktop margin-right-xxs\t \"\n\t\t\tdata-bi-name=\"ask-learn-assistant-entry\"\n\t\t\tdata-test-id=\"ask-learn-assistant-flyout-entry\"\n\t\t\tdata-ask-learn-flyout-entry\n\t\t\tdata-flyout-button=\"toggle\"\n\t\t\ttype=\"button\"\n\t\t\tstyle=\"min-width: max-content;\"\n\t\t\taria-expanded=\"false\"\n\t\t\taria-controls=\"ask-learn-flyout\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-chat-sparkle gradient-ask-learn-logo\"></span>\n\t\t\t</span>\n\t\t\t<span>Ask Learn</span>\n\t\t</button>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tid=\"ms--focus-mode-button\"\n\t\t\tdata-focus-mode\n\t\t\tdata-bi-name=\"focus-mode-entry\"\n\t\t\tclass=\"button button-sm flex-shrink-0 margin-right-xxs display-none display-inline-flex-desktop\"\n\t\t>\n\t\t\t<span class=\"icon font-size-lg\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-glasses\"></span>\n\t\t\t</span>\n\t\t\t<span>Focus mode</span>\n\t\t</button>\n\t \n\n\t\t\t<details class=\"popover popover-right\" id=\"article-header-page-actions-overflow\">\n\t\t\t\t<summary\n\t\t\t\t\tclass=\"justify-content-flex-start button button-clear button-sm button-primary inner-focus\"\n\t\t\t\t\taria-label=\"More actions\"\n\t\t\t\t\ttitle=\"More actions\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-more-vertical\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<div class=\"popover-content\">\n\t\t\t\t\t\n\t\t<button\n\t\t\tdata-page-action-item=\"overflow-mobile\"\n\t\t\ttype=\"button\"\n\t\t\tclass=\"button-block button-sm has-inner-focus button button-clear display-none-tablet justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"contents-expand\"\n\t\t\tdata-contents-button\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\">\n\t\t\t\t<span class=\"docon docon-editor-list-bullet\" aria-hidden=\"true\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"contents-expand-title\">Table of contents</span>\n\t\t</button>\n\t \n\t\t<a\n\t\t\tid=\"lang-link-overflow\"\n\t\t\tclass=\"button-sm has-inner-focus button button-clear button-block justify-content-flex-start text-align-left\"\n\t\t\tdata-bi-name=\"language-toggle\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-read-in-link\n\t\t\thref=\"#\"\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\" data-read-in-link-icon>\n\t\t\t\t<span class=\"docon docon-locale-globe\"></span>\n\t\t\t</span>\n\t\t\t<span data-read-in-link-text>Read in English</span>\n\t\t</a>\n\t \n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-clear button-sm button-block justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"collection\"\n\t\t\tdata-bi-name=\"collection\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"collection-status\">Add</span>\n\t\t</button>\n\t\n\t\t\t\t\t\n\t\t<button\n\t\t\ttype=\"button\"\n\t\t\tclass=\"collection button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\tdata-list-type=\"plan\"\n\t\t\tdata-bi-name=\"plan\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-check-hidden=\"true\"\n\t\t\tdata-popover-close\n\t\t\thidden\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-circle-addition\"></span>\n\t\t\t</span>\n\t\t\t<span class=\"plan-status\">Add to plan</span>\n\t\t</button>\n\t  \n\t\t<a\n\t\t\tdata-contenteditbtn\n\t\t\tclass=\"button button-clear button-block button-sm inner-focus justify-content-flex-start text-align-left text-decoration-none\"\n\t\t\tdata-bi-name=\"edit\"\n\t\t\t\n\t\t\thref=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/main/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\"\n\t\t\tdata-original_content_git_url=\"https://github.com/MicrosoftDocs/windows-powershell-docs/blob/live/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\"\n\t\t\tdata-original_content_git_url_template=\"{repo}/blob/{branch}/docset/winserver2025-ps/ActiveDirectory/Set-ADUser.md\"\n\t\t\tdata-pr_repo=\"\"\n\t\t\tdata-pr_branch=\"\"\n\t\t>\n\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-edit-outline\"></span>\n\t\t\t</span>\n\t\t\t<span>Edit</span>\n\t\t</a>\n\t\n\t\t\t\t\t\n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<h4 class=\"font-size-sm padding-left-xxs\">Share via</h4>\n\t\t\n\t\t\t\t\t<a\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-facebook\"\n\t\t\t\t\t\tdata-bi-name=\"facebook\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-facebook-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Facebook</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-twitter\"\n\t\t\t\t\t\tdata-bi-name=\"twitter\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-text\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-xlogo-share\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>x.com</span>\n\t\t\t\t\t</a>\n\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-linkedin\"\n\t\t\t\t\t\tdata-bi-name=\"linkedin\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-linked-in-logo\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>LinkedIn</span>\n\t\t\t\t\t</a>\n\t\t\t\t\t<a\n\t\t\t\t\t\thref=\"#\"\n\t\t\t\t\t\tclass=\"button button-clear button-sm inner-focus button-block justify-content-flex-start text-align-left text-decoration-none share-email\"\n\t\t\t\t\t\tdata-bi-name=\"email\"\n\t\t\t\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-mail-message\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Email</span>\n\t\t\t\t\t</a>\n\t\t\t  \n\t \n\t\t<hr class=\"margin-block-xxs\" />\n\t\t<button\n\t\t\tclass=\"button button-block button-clear button-sm justify-content-flex-start text-align-left inner-focus\"\n\t\t\ttype=\"button\"\n\t\t\tdata-bi-name=\"print\"\n\t\t\tdata-page-action-item=\"overflow-all\"\n\t\t\tdata-popover-close\n\t\t\tdata-print-page\n\t\t\tdata-check-hidden=\"true\"\n\t\t>\n\t\t\t<span class=\"icon color-primary\" aria-hidden=\"true\">\n\t\t\t\t<span class=\"docon docon-print\"></span>\n\t\t\t</span>\n\t\t\t<span>Print</span>\n\t\t</button>\n\t\n\t\t\t\t</div>\n\t\t\t</details>\n\t\t</div>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<!-- azure disclaimer -->\n\t\t\t\t\t\n\t\t\t\t\t<!-- privateUnauthorizedTemplate is hidden by default -->\n\t\t\t\t\t\n\t\t<div unauthorized-private-section data-bi-name=\"permission-content-unauthorized-private\" hidden>\n\t\t\t<hr class=\"hr margin-top-xs margin-bottom-sm\" />\n\t\t\t<div class=\"notification notification-info\">\n\t\t\t\t<div class=\"notification-content\">\n\t\t\t\t\t<p class=\"margin-top-none notification-title\">\n\t\t\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t\t\t<span class=\"docon docon-exclamation-circle-solid\" aria-hidden=\"true\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Note</span>\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined not-authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-sign-in\" href=\"#\" data-bi-name=\"permission-content-sign-in\">signing in</a> or <a  class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class=\"margin-top-none authentication-determined authenticated\">\n\t\t\t\t\t\tAccess to this page requires authorization. You can try <a class=\"docs-change-directory\" data-bi-name=\"permisson-content-change-directory\">changing directories</a>.\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t\t\t<div class=\"content\"></div>\n\t\t\t\t\t \n\t\t\t\t\t<div class=\"content\"><h1 data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\" class=\"margin-bottom-xs\">Set-ADUser</h1>\n\n\t<div class=\"margin-block-xxs\">\n\t\t<ul class=\"metadata page-metadata align-items-center\" data-bi-name=\"page info\">\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t\t\n\t\t</ul>\n\t</div>\n\n<div class=\"metadata\" data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">\n\t\t<dl class=\"attributeList\">\n\t\t\t<dt>Module:</dt>\n\t\t\t<dd><a href=\"./?view=windowsserver2025-ps\" data-linktype=\"relative-path\">ActiveDirectory Module</a></dd>\n\t\t</dl>\n</div>\n\n<nav id=\"center-doc-outline\" class=\"doc-outline is-hidden-desktop display-none-print margin-bottom-sm\" data-bi-name=\"intopic toc\" aria-label=\"\">\n  <h2 class=\"title is-6 margin-block-xs\"></h2>\n</nav>\n\n\n\t<div class=\"margin-block-sm\" data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">\n\t\t<p>Modifies an Active Directory user.</p>\n\n\t</div>\n\n\t<h2 id=\"syntax\" data-chunk-ids=\"identity,instance\">Syntax</h2>\n\t<h3 id=\"identity\" data-chunk-ids=\"identity\">\n\t\tIdentity\n\t</h3>\n\t<div data-chunk-ids=\"identity\">\n\t\t<pre><code class=\"lang-Syntax\">Set-ADUser\n    [-Identity] &lt;ADUser&gt;\n    [-WhatIf]\n    [-Confirm]\n    [-AccountExpirationDate &lt;DateTime&gt;]\n    [-AccountNotDelegated &lt;Boolean&gt;]\n    [-Add &lt;Hashtable&gt;]\n    [-AllowReversiblePasswordEncryption &lt;Boolean&gt;]\n    [-AuthenticationPolicy &lt;ADAuthenticationPolicy&gt;]\n    [-AuthenticationPolicySilo &lt;ADAuthenticationPolicySilo&gt;]\n    [-AuthType &lt;ADAuthType&gt;]\n    [-CannotChangePassword &lt;Boolean&gt;]\n    [-Certificates &lt;Hashtable&gt;]\n    [-ChangePasswordAtLogon &lt;Boolean&gt;]\n    [-City &lt;String&gt;]\n    [-Clear &lt;String[]&gt;]\n    [-Company &lt;String&gt;]\n    [-CompoundIdentitySupported &lt;Boolean&gt;]\n    [-Country &lt;String&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-Department &lt;String&gt;]\n    [-Description &lt;String&gt;]\n    [-DisplayName &lt;String&gt;]\n    [-Division &lt;String&gt;]\n    [-EmailAddress &lt;String&gt;]\n    [-EmployeeID &lt;String&gt;]\n    [-EmployeeNumber &lt;String&gt;]\n    [-Enabled &lt;Boolean&gt;]\n    [-Fax &lt;String&gt;]\n    [-GivenName &lt;String&gt;]\n    [-HomeDirectory &lt;String&gt;]\n    [-HomeDrive &lt;String&gt;]\n    [-HomePage &lt;String&gt;]\n    [-HomePhone &lt;String&gt;]\n    [-Initials &lt;String&gt;]\n    [-KerberosEncryptionType &lt;ADKerberosEncryptionType&gt;]\n    [-LogonWorkstations &lt;String&gt;]\n    [-Manager &lt;ADUser&gt;]\n    [-MobilePhone &lt;String&gt;]\n    [-Office &lt;String&gt;]\n    [-OfficePhone &lt;String&gt;]\n    [-Organization &lt;String&gt;]\n    [-OtherName &lt;String&gt;]\n    [-Partition &lt;String&gt;]\n    [-PassThru]\n    [-PasswordNeverExpires &lt;Boolean&gt;]\n    [-PasswordNotRequired &lt;Boolean&gt;]\n    [-POBox &lt;String&gt;]\n    [-PostalCode &lt;String&gt;]\n    [-PrincipalsAllowedToDelegateToAccount &lt;ADPrincipal[]&gt;]\n    [-ProfilePath &lt;String&gt;]\n    [-Remove &lt;Hashtable&gt;]\n    [-Replace &lt;Hashtable&gt;]\n    [-SamAccountName &lt;String&gt;]\n    [-ScriptPath &lt;String&gt;]\n    [-Server &lt;String&gt;]\n    [-ServicePrincipalNames &lt;Hashtable&gt;]\n    [-SmartcardLogonRequired &lt;Boolean&gt;]\n    [-State &lt;String&gt;]\n    [-StreetAddress &lt;String&gt;]\n    [-Surname &lt;String&gt;]\n    [-Title &lt;String&gt;]\n    [-TrustedForDelegation &lt;Boolean&gt;]\n    [-UserPrincipalName &lt;String&gt;]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\t<h3 id=\"instance\" data-chunk-ids=\"instance\">\n\t\tInstance\n\t</h3>\n\t<div data-chunk-ids=\"instance\">\n\t\t<pre><code class=\"lang-Syntax\">Set-ADUser\n    -Instance &lt;ADUser&gt;\n    [-WhatIf]\n    [-Confirm]\n    [-AuthType &lt;ADAuthType&gt;]\n    [-Credential &lt;PSCredential&gt;]\n    [-PassThru]\n    [-SamAccountName &lt;String&gt;]\n    [-Server &lt;String&gt;]\n    [&lt;CommonParameters&gt;]\n</code></pre>\n\n\t</div>\n\n\n\t<h2 id=\"description\" data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">Description</h2>\n\t<div data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">\n\t\t<p>The <code>Set-ADUser</code> cmdlet modifies the properties of an Active Directory user. You can modify\ncommonly used property values by using the cmdlet parameters. You can set property values that are\nnot associated with cmdlet parameters by using the <strong>Add</strong>, <strong>Remove</strong>, <strong>Replace</strong>, and <strong>Clear</strong>\nparameters.</p>\n<p>The <em>Identity</em> parameter specifies the Active Directory user to modify.\nYou can identify a user by its distinguished name, GUID, security identifier (SID), or Security Account Manager (SAM) account name.\nYou can also set the <em>Identity</em> parameter to an object variable such as <code>$&lt;localUserObject&gt;</code>, or you can pass an object through the pipeline to the <em>Identity</em> parameter.\nFor example, you can use the <strong>Get-ADUser</strong> cmdlet to retrieve a user object and then pass the object through the pipeline to the <strong>Set-ADUser</strong> cmdlet.</p>\n<p>The <em>Instance</em> parameter provides a way to update a user object by applying the changes made to a copy of the object.\nWhen you set the <em>Instance</em> parameter to a copy of an Active Directory user object that has been modified, the <strong>Set-ADUser</strong> cmdlet makes the same changes to the original user object.\nTo get a copy of the object to modify, use the <strong>Get-ADUser</strong> object.\nThe <em>Identity</em> parameter is not allowed when you use the <em>Instance</em> parameter.\nFor more information about the <em>Instance</em> parameter, see the <em>Instance</em> parameter description.</p>\n<p>Accounts created with the <strong>New-ADUser</strong> cmdlet are disabled if no password is provided.</p>\n<p>For AD LDS environments, the <em>Partition</em> parameter must be specified except in the following two conditions:</p>\n<ul>\n<li>The cmdlet is run from an Active Directory provider drive.</li>\n<li>A default naming context or partition is defined for the AD LDS environment.\nTo specify a default naming context for an AD LDS environment, set the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent object (<strong>nTDSDSA</strong>) for the AD LDS instance.</li>\n</ul>\n\n\t</div>\n\n\t<h2 id=\"examples\" data-chunk-ids=\"example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property\">Examples</h2>\n\t<h3 id=\"example-1-set-properties-for-a-user\" data-chunk-ids=\"example-1-set-properties-for-a-user\">Example 1: Set properties for a user</h3>\n\t<div data-chunk-ids=\"example-1-set-properties-for-a-user\">\n\t\t<pre><code class=\"lang-powershell\">$params = @{\n    Identity          = 'ChewDavid'\n    HomePage          = 'http://fabrikam.com/employees/ChewDavid'\n    LogonWorkstations = 'ChewDavid-DSKTOP,ChewDavid-LPTOP'\n}\nSet-ADUser @params\n</code></pre>\n<p>This command sets the specified user's <strong>homepage</strong> property to <a href=\"http://fabrikam.com/employees/ChewDavid\" data-linktype=\"external\">http://fabrikam.com/employees/ChewDavid</a> and the <strong>LogonWorkstations</strong> property to ChewDavid-DSKTOP,ChewDavid-LPTOP.</p>\n\n\t</div>\n\t<h3 id=\"example-2-set-properties-for-multiple-users\" data-chunk-ids=\"example-2-set-properties-for-multiple-users\">Example 2: Set properties for multiple users</h3>\n\t<div data-chunk-ids=\"example-2-set-properties-for-multiple-users\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Filter 'Name -like \"*\"' -SearchBase 'OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM' -Properties DisplayName | % {Set-ADUser $_ -DisplayName ($_.Surname + ' ' + $_.GivenName)}\n</code></pre>\n<p>This command gets all the users in the directory that are located in the OU=HumanResources,OU=UserAccounts,DC=FABRIKAM,DC=COM organizational unit.\nThe command sets the <strong>DisplayName</strong> property on these user objects to the concatenation of the <strong>Surname</strong> property and the <strong>GivenName</strong> property.</p>\n\n\t</div>\n\t<h3 id=\"example-3-set-properties\" data-chunk-ids=\"example-3-set-properties\">Example 3: Set properties</h3>\n\t<div data-chunk-ids=\"example-3-set-properties\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Set-ADUser -Identity GlenJohn -Replace @{title=\"director\";mail=\"<EMAIL>\"}\n</code></pre>\n<p>This command sets the specified user's <strong>title</strong> property to director and the <strong>mail</strong> <NAME_EMAIL>.</p>\n\n\t</div>\n\t<h3 id=\"example-4-modify-a-user-othermailbox-property\" data-chunk-ids=\"example-4-modify-a-user-othermailbox-property\">Example 4: Modify a user other<wbr>Mailbox property</h3>\n\t<div data-chunk-ids=\"example-4-modify-a-user-othermailbox-property\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Set-ADUser -Identity GlenJohn -Remove @{otherMailbox=\"glen.john\"} -Add @{url=\"fabrikam.com\"} -Replace @{title=\"manager\"} -Clear description\n</code></pre>\n<p>This command modifies the user with the SAM account name GlenJohn's object by removing glen.john from the <strong>otherMailbox</strong> property, adding fabrikam.com to the <strong>url</strong> property, replacing the <strong>title</strong> property with manager, and clearing the <strong>description</strong> property.</p>\n\n\t</div>\n\t<h3 id=\"example-5-set-user-properties-to-a-local-instance\" data-chunk-ids=\"example-5-set-user-properties-to-a-local-instance\">Example 5: Set user properties to a local instance</h3>\n\t<div data-chunk-ids=\"example-5-set-user-properties-to-a-local-instance\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; $User = Get-ADUser -Identity GlenJohn -Properties mail,department\nPS C:\\&gt; $User.mail = \"<EMAIL>\"\nPS C:\\&gt; $User.department = \"Accounting\"\nPS C:\\&gt; Set-ADUser -Instance $User\n</code></pre>\n<p>This example sets the <strong>mail</strong> and <strong>department</strong> properties on the user object with the SAM account name GlenJohn by using the <em>Instance</em> parameter.</p>\n\n\t</div>\n\t<h3 id=\"example-6-set-attributes-for-a-user\" data-chunk-ids=\"example-6-set-attributes-for-a-user\">Example 6: Set attributes for a user</h3>\n\t<div data-chunk-ids=\"example-6-set-attributes-for-a-user\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; $Hours = New-Object byte[] 21\nPS C:\\&gt; $Hours[5] = 255; $Hours[8] = 255; $Hours[11] = 255; $Hours[14] = 255; $Hours[17] = 255;\nPS C:\\&gt; $Hours[6] = 1; $Hours[9] = 1; $Hours[12] = 1; $Hours[15] = 1; $Hours[18] = 1;\nPS C:\\&gt; $ReplaceHashTable = New-Object HashTable\nPS C:\\&gt; $ReplaceHashTable.Add(\"logonHours\", $Hours)\nPS C:\\&gt; $ReplaceHashTable.Add(\"description\", \"Sarah Davis can only logon from Monday through Friday from 8:00 AM to 5:00 PM\")\nPS C:\\&gt; Set-ADUser -Identity \"SarahDavis\" -Replace $ReplaceHashTable\n</code></pre>\n<p>This example sets the user logon hours to Monday through Friday from 8:00 AM to 5:00 PM and adds a description.\nIt updates the <strong>logonHours</strong> attribute with the specified byte array and the <strong>description</strong> attribute with the specified string.</p>\n\n\t</div>\n\t<h3 id=\"example-7-set-a-property-for-a-user\" data-chunk-ids=\"example-7-set-a-property-for-a-user\">Example 7: Set a property for a user</h3>\n\t<div data-chunk-ids=\"example-7-set-a-property-for-a-user\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; $Manager = Get-ADUser -Identity GlenJohn -Server Corp-DC01\nPS C:\\&gt; Set-ADUser -Identity ChewDavid -Manager $Manager -Server Branch-DC02\n</code></pre>\n<p>This example sets the <strong>Manager</strong> property for the user with the SAM account name of ChewDavid where the manager, GlenJohn, is a user in another domain.</p>\n\n\t</div>\n\t<h3 id=\"example-8-get-a-user-and-set-a-property\" data-chunk-ids=\"example-8-get-a-user-and-set-a-property\">Example 8: Get a user and set a property</h3>\n\t<div data-chunk-ids=\"example-8-get-a-user-and-set-a-property\">\n\t\t<pre><code class=\"lang-powershell\">PS C:\\&gt; Get-ADUser -Identity \"DavidChew\" | Set-ADUser -Manager \"ElisaDaugherty\"\n</code></pre>\n<p>This command modifies the <strong>Manager</strong> property for the DavidChew user.\nThe command uses the <strong>Get-ADUser</strong> cmdlet to get the user DavidChew, and then passes the object to the current cmdlet by using the pipeline operator.</p>\n\n\t</div>\n\n\t<h2 id=\"parameters\" data-chunk-ids=\"accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">Parameters</h2>\n\t\t<h3 id=\"-accountexpirationdate\" data-chunk-ids=\"accountexpirationdate\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Account<wbr>Expiration<wbr>Date</h3>\n\t\t<p>Specifies the expiration date for an account.\nThis parameter sets the AccountExpirationDate property of an account object.\nThe LDAP display name (ldapDisplayName) for this property is accountExpires.</p>\n<p>Use the <strong>DateTime</strong> syntax when you specify this parameter.\nTime is assumed to be local time unless otherwise specified.\nWhen a time value is not specified, the time is assumed to 12:00:00 AM local time.\nWhen a date is not specified, the date is assumed to be the current date.</p>\n\n\n\t\t<h4 id=\"accountexpirationdate-properties\" data-chunk-ids=\"accountexpirationdate\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"accountexpirationdate\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">DateTime</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"accountexpirationdate-sets\" data-chunk-ids=\"accountexpirationdate\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"accountexpirationdate\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-accountnotdelegated\" data-chunk-ids=\"accountnotdelegated\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Account<wbr>Not<wbr>Delegated</h3>\n\t\t<p>Indicates whether the security context of the user is delegated to a service.\nWhen this parameter is set to $True, the security context of the account is not delegated to a service even when the service account is set as trusted for Kerberos delegation.\nThis parameter sets the <strong>AccountNotDelegated</strong> property for an Active Directory account.\nThis parameter also sets the <strong>ADS_UF_NOT_DELEGATED</strong> flag of the Active Directory User Account Control (UAC) attribute.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"accountnotdelegated-properties\" data-chunk-ids=\"accountnotdelegated\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"accountnotdelegated\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"accountnotdelegated-sets\" data-chunk-ids=\"accountnotdelegated\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"accountnotdelegated\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-add\" data-chunk-ids=\"add\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Add</h3>\n\t\t<p>Specifies values to add to an object property. Use this parameter to add one or more values to a\nproperty that cannot be modified using a cmdlet parameter. To modify an object property, you must\nuse the LDAP display name. You can specify multiple values to a property by specifying a\ncomma-separated list of values, and more than one property by separating them using a semicolon. If\nany of the properties have a null or empty value the cmdlet will return an error. The format for\nthis parameter is:</p>\n<p><code>-Add @{Attribute1LDAPDisplayName=value1, value2, ...;   Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}</code></p>\n<p>When you use the <em>Add</em>, <em>Remove</em>, <em>Replace</em>, and <em>Clear</em> parameters together, the operations will be performed in the following order:</p>\n<ul>\n<li><strong>Remove</strong></li>\n<li><strong>Add</strong></li>\n<li><strong>Replace</strong></li>\n<li><strong>Clear</strong></li>\n</ul>\n\n\n\t\t<h4 id=\"add-properties\" data-chunk-ids=\"add\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"add\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Hashtable</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"add-sets\" data-chunk-ids=\"add\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"add\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-allowreversiblepasswordencryption\" data-chunk-ids=\"allowreversiblepasswordencryption\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Allow<wbr>Reversible<wbr>Password<wbr>Encryption</h3>\n\t\t<p>Indicates whether reversible password encryption is allowed for the account. This parameter sets the\n<strong>AllowReversiblePasswordEncryption</strong> property of the account. This parameter also sets the\n<strong>ADS_UF_ENCRYPTED_TEXT_PASSWORD_ALLOWED</strong> flag of the Active Directory User Account Control (UAC)\nattribute. The acceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"allowreversiblepasswordencryption-properties\" data-chunk-ids=\"allowreversiblepasswordencryption\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"allowreversiblepasswordencryption\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"allowreversiblepasswordencryption-sets\" data-chunk-ids=\"allowreversiblepasswordencryption\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"allowreversiblepasswordencryption\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-authenticationpolicy\" data-chunk-ids=\"authenticationpolicy\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Authentication<wbr>Policy</h3>\n\t\t<p>Specifies an Active Directory Domain Services authentication policy object.\nSpecify the authentication policy object in one of the following formats:</p>\n<ul>\n<li>Distinguished name</li>\n<li>GUID</li>\n<li>Name</li>\n</ul>\n<p>This parameter can also get this object through the pipeline or you can set this parameter to an\nobject instance.</p>\n<p>The cmdlet searches the default naming context or partition to find the object.\nIf the cmdlet finds two or more objects, the cmdlet returns a non-terminating error.</p>\n\n\n\t\t<h4 id=\"authenticationpolicy-properties\" data-chunk-ids=\"authenticationpolicy\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"authenticationpolicy\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADAuthenticationPolicy</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"authenticationpolicy-sets\" data-chunk-ids=\"authenticationpolicy\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"authenticationpolicy\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-authenticationpolicysilo\" data-chunk-ids=\"authenticationpolicysilo\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Authentication<wbr>Policy<wbr>Silo</h3>\n\t\t<p>Specifies an Active Directory Domain Services authentication policy silo object.\nSpecify the authentication policy silo object in one of the following formats:</p>\n<ul>\n<li>Distinguished name</li>\n<li>GUID</li>\n<li>Name</li>\n</ul>\n<p>This parameter can also get this object through the pipeline or you can set this parameter to an\nobject instance.</p>\n<p>The cmdlet searches the default naming context or partition to find the object.\nIf the cmdlet finds two or more objects, the cmdlet returns a non-terminating error.</p>\n\n\n\t\t<h4 id=\"authenticationpolicysilo-properties\" data-chunk-ids=\"authenticationpolicysilo\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"authenticationpolicysilo\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADAuthenticationPolicySilo</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"authenticationpolicysilo-sets\" data-chunk-ids=\"authenticationpolicysilo\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"authenticationpolicysilo\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-authtype\" data-chunk-ids=\"authtype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Auth<wbr>Type</h3>\n\t\t<p>Specifies the authentication method to use.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>Negotiate or 0</li>\n<li>Basic or 1</li>\n</ul>\n<p>The default authentication method is Negotiate.</p>\n<p>A Secure Sockets Layer (SSL) connection is required for the Basic authentication method.</p>\n\n\n\t\t<h4 id=\"authtype-properties\" data-chunk-ids=\"authtype\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"authtype\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADAuthType</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>Negotiate, Basic</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"authtype-sets\" data-chunk-ids=\"authtype\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"authtype\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-cannotchangepassword\" data-chunk-ids=\"cannotchangepassword\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Cannot<wbr>Change<wbr>Password</h3>\n\t\t<p>Indicates whether the account password can be changed.\nThis parameter sets the <strong>CannotChangePassword</strong> property of an account.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"cannotchangepassword-properties\" data-chunk-ids=\"cannotchangepassword\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"cannotchangepassword\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"cannotchangepassword-sets\" data-chunk-ids=\"cannotchangepassword\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"cannotchangepassword\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-certificates\" data-chunk-ids=\"certificates\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Certificates</h3>\n\t\t<p>Specifies an array of certificates. The cmdlet modifies the DER-encoded X.509v3 certificates of the\naccount. These certificates include the public key certificates issued to this account by the\nMicrosoft Certificate Service. This parameter sets the <strong>Certificates</strong> property of the account\nobject. The Lightweight Directory Access Protocol (LDAP) display name (<strong>ldapDisplayName</strong>) for this\nproperty is userCertificate.</p>\n<p>To add values:</p>\n<p><code>-Certificates @{Add=value1,value2,...}</code></p>\n<p>To remove values:</p>\n<p><code>-Certificates @{Remove=value3,value4,...}</code></p>\n<p>To replace values:</p>\n<p><code>-Certificates @{Replace=value1,value2,...}</code></p>\n<p>To clear all values:</p>\n<p><code>-Certificates $Null</code></p>\n<p>You can specify more than one operation by using a list separated by semicolons.\nFor example, use the following syntax to add and remove <strong>Certificates</strong> values:</p>\n<p><code>-Certificates @{Add=value1;Remove=value3}</code></p>\n<p>The operators are applied in the following sequence:</p>\n<ul>\n<li>Remove</li>\n<li>Add</li>\n<li>Replace</li>\n</ul>\n\n\n\t\t<h4 id=\"certificates-properties\" data-chunk-ids=\"certificates\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"certificates\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Hashtable</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"certificates-sets\" data-chunk-ids=\"certificates\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"certificates\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-changepasswordatlogon\" data-chunk-ids=\"changepasswordatlogon\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Change<wbr>Password<wbr>AtLogon</h3>\n\t\t<p>Indicates whether a password must be changed during the next logon attempt.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"changepasswordatlogon-properties\" data-chunk-ids=\"changepasswordatlogon\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"changepasswordatlogon\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"changepasswordatlogon-sets\" data-chunk-ids=\"changepasswordatlogon\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"changepasswordatlogon\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-city\" data-chunk-ids=\"city\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-City</h3>\n\t\t<p>Specifies the user's town or city.\nThis parameter sets the <strong>City</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is l.</p>\n\n\n\t\t<h4 id=\"city-properties\" data-chunk-ids=\"city\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"city\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"city-sets\" data-chunk-ids=\"city\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"city\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-clear\" data-chunk-ids=\"clear\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Clear</h3>\n\t\t<p>Specifies an array of object properties that are cleared in the directory. Use this parameter to\nclear one or more values of a property that cannot be modified using a cmdlet parameter. To modify\nan object property, you must use the LDAP display name. You can modify more than one property by\nspecifying a comma-separated list. The format for this parameter is:</p>\n<p><code>-Clear Attribute1LDAPDisplayName, Attribute2LDAPDisplayName</code></p>\n<p>When you use the <strong>Add</strong>, <strong>Remove</strong>, <strong>Replace</strong>, and <strong>Clear</strong> parameters together, the\noperations are performed in the following order:</p>\n<ul>\n<li><strong>Remove</strong></li>\n<li><strong>Add</strong></li>\n<li><strong>Replace</strong></li>\n<li><strong>Clear</strong></li>\n</ul>\n\n\n\t\t<h4 id=\"clear-properties\" data-chunk-ids=\"clear\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"clear\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><p><span class=\"no-loc xref\">String</span><span>[</span><span>]</span></p>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"clear-sets\" data-chunk-ids=\"clear\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"clear\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-company\" data-chunk-ids=\"company\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Company</h3>\n\t\t<p>Specifies the user's company.\nThis parameter sets the <strong>Company</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is company.</p>\n\n\n\t\t<h4 id=\"company-properties\" data-chunk-ids=\"company\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"company\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"company-sets\" data-chunk-ids=\"company\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"company\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-compoundidentitysupported\" data-chunk-ids=\"compoundidentitysupported\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Compound<wbr>Identity<wbr>Supported</h3>\n\t\t<p>Indicates whether an account supports Kerberos service tickets which includes the authorization data\nfor the user's device. This value sets the compound identity supported flag of the Active Directory\n<strong>msDS-SupportedEncryptionTypes</strong> attribute. The acceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n<div class=\"WARNING\">\n<p>Warning</p>\n<p>Domain-joined Windows systems and services such as clustering manage their own\n<strong>msDS-SupportedEncryptionTypes</strong> attribute. Therefore any changes to the flag on the\n<strong>msDS-SupportedEncryptionTypes</strong> attribute are overwritten by the service or system that manages\nthe setting.</p>\n</div>\n\n\n\t\t<h4 id=\"compoundidentitysupported-properties\" data-chunk-ids=\"compoundidentitysupported\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"compoundidentitysupported\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"compoundidentitysupported-sets\" data-chunk-ids=\"compoundidentitysupported\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"compoundidentitysupported\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-confirm\" data-chunk-ids=\"confirm\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Confirm</h3>\n\t\t<p>Prompts you for confirmation before running the cmdlet.</p>\n\n\n\t\t<h4 id=\"confirm-properties\" data-chunk-ids=\"confirm\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"confirm\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">SwitchParameter</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Aliases:</td><td>cf</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"confirm-sets\" data-chunk-ids=\"confirm\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"confirm\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-country\" data-chunk-ids=\"country\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Country</h3>\n\t\t<p>Specifies the country or region code for the user's language of choice.\nThis parameter sets the <strong>Country</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is c.\nThis value is not used by Windows 2000.</p>\n\n\n\t\t<h4 id=\"country-properties\" data-chunk-ids=\"country\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"country\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"country-sets\" data-chunk-ids=\"country\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"country\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-credential\" data-chunk-ids=\"credential\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Credential</h3>\n\t\t<p>Specifies the user account credentials to use to perform this task. The default credentials are the\ncredentials of the currently logged on user unless the cmdlet is run from an Active Directory\nPowerShell provider drive. If the cmdlet is run from such a provider drive, the account associated\nwith the drive is the default.</p>\n<p>To specify this parameter, you can type a user name, such as <code>User1</code> or <code>Domain01\\User01</code> or you can specify a <strong>PSCredential</strong> object.\nIf you specify a user name for this parameter, the cmdlet prompts for a password.</p>\n<p>You can also create a <strong>PSCredential</strong> object by using a script or by using the <code>Get-Credential</code>\ncmdlet. You can then set the <strong>Credential</strong> parameter to the <strong>PSCredential</strong> object.</p>\n<p>If the acting credentials do not have directory-level permission to perform the task, Active\nDirectory PowerShell returns a terminating error.</p>\n\n\n\t\t<h4 id=\"credential-properties\" data-chunk-ids=\"credential\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"credential\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">PSCredential</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"credential-sets\" data-chunk-ids=\"credential\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"credential\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-department\" data-chunk-ids=\"department\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Department</h3>\n\t\t<p>Specifies the user's department.\nThis parameter sets the <strong>Department</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is department.</p>\n\n\n\t\t<h4 id=\"department-properties\" data-chunk-ids=\"department\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"department\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"department-sets\" data-chunk-ids=\"department\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"department\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-description\" data-chunk-ids=\"description\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Description</h3>\n\t\t<p>Specifies a description of the object.\nThis parameter sets the value of the <strong>Description</strong> property for the user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is description.</p>\n\n\n\t\t<h4 id=\"description-properties\" data-chunk-ids=\"description\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"description\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"description-sets\" data-chunk-ids=\"description\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"description\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-displayname\" data-chunk-ids=\"displayname\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Display<wbr>Name</h3>\n\t\t<p>Specifies the display name of the object.\nThis parameter sets the <strong>DisplayName</strong> property of the user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is displayName.</p>\n\n\n\t\t<h4 id=\"displayname-properties\" data-chunk-ids=\"displayname\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"displayname\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"displayname-sets\" data-chunk-ids=\"displayname\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"displayname\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-division\" data-chunk-ids=\"division\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Division</h3>\n\t\t<p>Specifies the user's division.\nThis parameter sets the <strong>Division</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is division.</p>\n\n\n\t\t<h4 id=\"division-properties\" data-chunk-ids=\"division\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"division\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"division-sets\" data-chunk-ids=\"division\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"division\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-emailaddress\" data-chunk-ids=\"emailaddress\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Email<wbr>Address</h3>\n\t\t<p>Specifies the user's e-mail address.\nThis parameter sets the <strong>EmailAddress</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is mail.</p>\n\n\n\t\t<h4 id=\"emailaddress-properties\" data-chunk-ids=\"emailaddress\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"emailaddress\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"emailaddress-sets\" data-chunk-ids=\"emailaddress\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"emailaddress\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-employeeid\" data-chunk-ids=\"employeeid\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-EmployeeID</h3>\n\t\t<p>Specifies the user's employee ID.\nThis parameter sets the <strong>EmployeeID</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is employeeID.</p>\n\n\n\t\t<h4 id=\"employeeid-properties\" data-chunk-ids=\"employeeid\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"employeeid\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"employeeid-sets\" data-chunk-ids=\"employeeid\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"employeeid\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-employeenumber\" data-chunk-ids=\"employeenumber\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Employee<wbr>Number</h3>\n\t\t<p>Specifies the user's employee number.\nThis parameter sets the <strong>EmployeeNumber</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is employeeNumber.</p>\n\n\n\t\t<h4 id=\"employeenumber-properties\" data-chunk-ids=\"employeenumber\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"employeenumber\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"employeenumber-sets\" data-chunk-ids=\"employeenumber\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"employeenumber\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-enabled\" data-chunk-ids=\"enabled\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Enabled</h3>\n\t\t<p>Indicates whether an account is enabled. An enabled account requires a password. This parameter sets\nthe <strong>Enabled</strong> property for an account object. This parameter also sets the\n<strong>ADS_UF_ACCOUNTDISABLE</strong> flag of the Active Directory User Account Control (UAC) attribute. The\nacceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"enabled-properties\" data-chunk-ids=\"enabled\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"enabled\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"enabled-sets\" data-chunk-ids=\"enabled\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"enabled\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-fax\" data-chunk-ids=\"fax\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Fax</h3>\n\t\t<p>Specifies the user's fax phone number.\nThis parameter sets the <strong>Fax</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is facsimileTelephoneNumber.</p>\n\n\n\t\t<h4 id=\"fax-properties\" data-chunk-ids=\"fax\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"fax\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"fax-sets\" data-chunk-ids=\"fax\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"fax\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-givenname\" data-chunk-ids=\"givenname\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Given<wbr>Name</h3>\n\t\t<p>Specifies the user's given name.\nThis parameter sets the <strong>GivenName</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is givenName.</p>\n\n\n\t\t<h4 id=\"givenname-properties\" data-chunk-ids=\"givenname\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"givenname\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"givenname-sets\" data-chunk-ids=\"givenname\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"givenname\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-homedirectory\" data-chunk-ids=\"homedirectory\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Home<wbr>Directory</h3>\n\t\t<p>Specifies a user's home directory.\nThis parameter sets the <strong>HomeDirectory</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is homeDirectory.</p>\n\n\n\t\t<h4 id=\"homedirectory-properties\" data-chunk-ids=\"homedirectory\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"homedirectory\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"homedirectory-sets\" data-chunk-ids=\"homedirectory\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"homedirectory\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-homedrive\" data-chunk-ids=\"homedrive\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Home<wbr>Drive</h3>\n\t\t<p>Specifies a drive that is associated with the UNC path defined by the <strong>HomeDirectory</strong> property.\nThe drive letter is specified as <code>&lt;DriveLetter&gt;</code>: where <code>&lt;DriveLetter&gt;</code> indicates the letter of the\ndrive to associate. The <code>&lt;DriveLetter&gt;</code> must be a single, uppercase letter and the colon is\nrequired. This parameter sets the <strong>HomeDrive</strong> property of the user object. The LDAP display name\n(<strong>ldapDisplayName</strong>) for this property is homeDrive.</p>\n\n\n\t\t<h4 id=\"homedrive-properties\" data-chunk-ids=\"homedrive\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"homedrive\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"homedrive-sets\" data-chunk-ids=\"homedrive\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"homedrive\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-homepage\" data-chunk-ids=\"homepage\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Home<wbr>Page</h3>\n\t\t<p>Specifies the URL of the home page of the object.\nThis parameter sets the <strong>homePage</strong> property of an Active Directory object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is wWWHomePage.</p>\n\n\n\t\t<h4 id=\"homepage-properties\" data-chunk-ids=\"homepage\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"homepage\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"homepage-sets\" data-chunk-ids=\"homepage\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"homepage\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-homephone\" data-chunk-ids=\"homephone\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Home<wbr>Phone</h3>\n\t\t<p>Specifies the user's home telephone number.\nThis parameter sets the <strong>HomePhone</strong> property of a user.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is homePhone.</p>\n\n\n\t\t<h4 id=\"homephone-properties\" data-chunk-ids=\"homephone\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"homephone\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"homephone-sets\" data-chunk-ids=\"homephone\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"homephone\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-identity\" data-chunk-ids=\"identity\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Identity</h3>\n\t\t<p>Specifies an Active Directory user object by providing one of the following property values.\nThe identifier in parentheses is the LDAP display name for the attribute.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>A distinguished name</li>\n<li>A GUID (<strong>objectGUID</strong>)</li>\n<li>A security identifier (<strong>objectSid</strong>)</li>\n<li>A SAM account name (<strong>sAMAccountName</strong>)</li>\n</ul>\n<p>The cmdlet searches the default naming context or partition to find the object.\nIf two or more objects are found, the cmdlet returns a non-terminating error.</p>\n<p>This parameter can also get this object through the pipeline or you can set this parameter to an\nobject instance.</p>\n\n\n\t\t<h4 id=\"identity-properties\" data-chunk-ids=\"identity\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"identity\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADUser</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"identity-sets\" data-chunk-ids=\"identity\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"identity\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>0</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-initials\" data-chunk-ids=\"initials\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Initials</h3>\n\t\t<p>Specifies the initials that represent part of a user's name.\nYou can use this value for the user's middle initial.\nThis parameter sets the <strong>Initials</strong> property of a user.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is initials.</p>\n\n\n\t\t<h4 id=\"initials-properties\" data-chunk-ids=\"initials\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"initials\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"initials-sets\" data-chunk-ids=\"initials\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"initials\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-instance\" data-chunk-ids=\"instance\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Instance</h3>\n\t\t<p>Specifies an <strong>ADUser</strong> object that identifies the Active Directory user object that should be\nmodified and the set of changes that should be made to that object. When this parameter is\nspecified, any modifications made to the <strong>ADUser</strong> object are also made to the corresponding Active\nDirectory object. The cmdlet only updates the object properties that have changed.</p>\n<p>The <strong>ADUser</strong> object specified as the value of the <strong>Instance</strong> parameter must have been retrieved\nby using the <code>Get-ADUser</code> cmdlet. When you specify the <strong>Instance</strong> parameter, you cannot specify\nother parameters that set individual properties on the object.</p>\n\n\n\t\t<h4 id=\"instance-properties\" data-chunk-ids=\"instance\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"instance\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADUser</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"instance-sets\" data-chunk-ids=\"instance\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"instance\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tInstance \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>True</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-kerberosencryptiontype\" data-chunk-ids=\"kerberosencryptiontype\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Kerberos<wbr>Encryption<wbr>Type</h3>\n\t\t<p>Specifies whether an account supports Kerberos encryption types which are used during creation of\nservice tickets. This value sets the encryption types supported flags of the Active Directory\n<strong>msDS-SupportedEncryptionTypes</strong> attribute. The acceptable values for this parameter are:</p>\n<ul>\n<li><code>None</code></li>\n<li><code>DES</code></li>\n<li><code>RC4</code></li>\n<li><code>AES128</code></li>\n<li><code>AES256</code></li>\n</ul>\n<p><code>None</code> removes all encryption types from the account, resulting in the KDC being unable to issue\nservice tickets for services using the account.</p>\n<p>DES is a weak encryption type that is not supported by default since Windows 7 and Windows Server\n2008 R2.</p>\n<div class=\"WARNING\">\n<p>Warning</p>\n<p>Domain-joined Windows systems and services such as clustering manage their own\n<strong>msDS-SupportedEncryptionTypes</strong> attribute. Therefore any changes to the flag on the\n<strong>msDS-SupportedEncryptionTypes</strong> attribute are overwritten by the service or system that manages\nthe setting.</p>\n</div>\n\n\n\t\t<h4 id=\"kerberosencryptiontype-properties\" data-chunk-ids=\"kerberosencryptiontype\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"kerberosencryptiontype\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADKerberosEncryptionType</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Accepted values:</td><td>None, DES, RC4, AES128, AES256</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"kerberosencryptiontype-sets\" data-chunk-ids=\"kerberosencryptiontype\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"kerberosencryptiontype\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-logonworkstations\" data-chunk-ids=\"logonworkstations\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Logon<wbr>Workstations</h3>\n\t\t<p>Specifies the computers that the user can access. To specify more than one computer, create a\nsingle comma-separated list. You can identify a computer by using the Security Account Manager\n(SAM) account name (<strong>sAMAccountName</strong>) or the DNS host name of the computer. The SAM account name\nis the same as the NetBIOS name of the computer.</p>\n<p>The LDAP display name (<strong>ldapDisplayName</strong>) for this property is userWorkStations.</p>\n\n\n\t\t<h4 id=\"logonworkstations-properties\" data-chunk-ids=\"logonworkstations\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"logonworkstations\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"logonworkstations-sets\" data-chunk-ids=\"logonworkstations\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"logonworkstations\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-manager\" data-chunk-ids=\"manager\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Manager</h3>\n\t\t<p>Specifies the user's manager.\nThis parameter sets the <strong>Manager</strong> property of a user object.\nThis parameter is set by providing one of the following property values.\nNote: The identifier in parentheses is the LDAP display name for the property.\nThe acceptable values for this parameter are:</p>\n<ul>\n<li>A distinguished name</li>\n<li>A GUID (<strong>objectGUID</strong>)</li>\n<li>A security identifier (<strong>objectSid</strong>)</li>\n<li>A SAM account name (<strong>sAMAccountName</strong>)</li>\n</ul>\n<p>The LDAP display name (<strong>ldapDisplayName</strong>) of this property is manager.</p>\n\n\n\t\t<h4 id=\"manager-properties\" data-chunk-ids=\"manager\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"manager\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">ADUser</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"manager-sets\" data-chunk-ids=\"manager\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"manager\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-mobilephone\" data-chunk-ids=\"mobilephone\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Mobile<wbr>Phone</h3>\n\t\t<p>Specifies the user's mobile phone number.\nThis parameter sets the <strong>MobilePhone</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is mobile.</p>\n\n\n\t\t<h4 id=\"mobilephone-properties\" data-chunk-ids=\"mobilephone\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"mobilephone\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"mobilephone-sets\" data-chunk-ids=\"mobilephone\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"mobilephone\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-office\" data-chunk-ids=\"office\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Office</h3>\n\t\t<p>Specifies the location of the user's office or place of business.\nThis parameter sets the <strong>Office</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is physicalDeliveryOfficeName.</p>\n\n\n\t\t<h4 id=\"office-properties\" data-chunk-ids=\"office\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"office\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"office-sets\" data-chunk-ids=\"office\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"office\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-officephone\" data-chunk-ids=\"officephone\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Office<wbr>Phone</h3>\n\t\t<p>Specifies the user's office telephone number.\nThis parameter sets the <strong>OfficePhone</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is telephoneNumber.</p>\n\n\n\t\t<h4 id=\"officephone-properties\" data-chunk-ids=\"officephone\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"officephone\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"officephone-sets\" data-chunk-ids=\"officephone\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"officephone\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-organization\" data-chunk-ids=\"organization\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Organization</h3>\n\t\t<p>Specifies the user's organization.\nThis parameter sets the <strong>Organization</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is o.</p>\n\n\n\t\t<h4 id=\"organization-properties\" data-chunk-ids=\"organization\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"organization\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"organization-sets\" data-chunk-ids=\"organization\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"organization\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-othername\" data-chunk-ids=\"othername\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Other<wbr>Name</h3>\n\t\t<p>Specifies a name in addition to a user's given name and surname, such as the user's middle name.\nThis parameter sets the <strong>OtherName</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is middleName.</p>\n\n\n\t\t<h4 id=\"othername-properties\" data-chunk-ids=\"othername\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"othername\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"othername-sets\" data-chunk-ids=\"othername\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"othername\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-partition\" data-chunk-ids=\"partition\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Partition</h3>\n\t\t<p>Specifies the distinguished name of an Active Directory partition.\nThe distinguished name must be one of the naming contexts on the current directory server.\nThe cmdlet searches this partition to find the object defined by the <strong>Identity</strong> parameter.</p>\n<p>In many cases, a default value is used for the <strong>Partition</strong> parameter if no value is specified.\nThe rules for determining the default value are given below. Note that rules listed first are\nevaluated first and when a default value can be determined, no further rules are evaluated.</p>\n<p>In AD DS environments, a default value for <strong>Partition</strong> are set in the following cases:</p>\n<ul>\n<li>If the <strong>Identity</strong> parameter is set to a distinguished name, the default value of <strong>Partition</strong>\nis automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <strong>Partition</strong> is\nautomatically generated from the current path in the drive.</li>\n<li>If none of the previous cases apply, the default value of <strong>Partition</strong> is set to the default\npartition or naming context of the target domain.</li>\n</ul>\n<p>In AD LDS environments, a default value for <strong>Partition</strong> will be set in the following cases:</p>\n<ul>\n<li>If the <strong>Identity</strong> parameter is set to a distinguished name, the default value of <strong>Partition</strong>\nis automatically generated from this distinguished name.</li>\n<li>If running cmdlets from an Active Directory provider drive, the default value of <strong>Partition</strong> is\nautomatically generated from the current path in the drive.</li>\n<li>If the target AD LDS instance has a default naming context, the default value of <strong>Partition</strong> is\nset to the default naming context. To specify a default naming context for an AD LDS environment,\nset the <strong>msDS-defaultNamingContext</strong> property of the Active Directory directory service agent\nobject (<strong>nTDSDSA</strong>) for the AD LDS instance.</li>\n<li>If none of the previous cases apply, the <strong>Partition</strong> parameter does not take any default value.</li>\n</ul>\n\n\n\t\t<h4 id=\"partition-properties\" data-chunk-ids=\"partition\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"partition\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"partition-sets\" data-chunk-ids=\"partition\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"partition\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-passthru\" data-chunk-ids=\"passthru\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Pass<wbr>Thru</h3>\n\t\t<p>Returns an object representing the item with which you are working.\nBy default, this cmdlet does not generate any output.</p>\n\n\n\t\t<h4 id=\"passthru-properties\" data-chunk-ids=\"passthru\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"passthru\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">SwitchParameter</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"passthru-sets\" data-chunk-ids=\"passthru\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"passthru\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-passwordneverexpires\" data-chunk-ids=\"passwordneverexpires\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Password<wbr>Never<wbr>Expires</h3>\n\t\t<p>Specifies whether the password of an account can expire. This parameter sets the\n<strong>PasswordNeverExpires</strong> property of an account object. This parameter also sets the\n<strong>ADS_UF_DONT_EXPIRE_PASSWD</strong> flag of the Active Directory User Account Control attribute. The\nacceptable values for this parameter are:</p>\n<ul>\n<li><code>$False</code> or <code>0</code></li>\n<li><code>$True</code> or <code>1</code></li>\n</ul>\n<div class=\"NOTE\">\n<p>Note</p>\n<p>This parameter cannot be set to <code>$True</code> or <code>1</code> for an account that also has the\n<strong>ChangePasswordAtLogon</strong> property set to <code>$True</code>.</p>\n</div>\n\n\n\t\t<h4 id=\"passwordneverexpires-properties\" data-chunk-ids=\"passwordneverexpires\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"passwordneverexpires\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"passwordneverexpires-sets\" data-chunk-ids=\"passwordneverexpires\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"passwordneverexpires\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-passwordnotrequired\" data-chunk-ids=\"passwordnotrequired\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Password<wbr>Not<wbr>Required</h3>\n\t\t<p>Specifies whether the account requires a password. This parameter sets the <strong>PasswordNotRequired</strong>\nproperty of an account, such as a user or computer account. This parameter also sets the\n<strong>ADS_UF_PASSWD_NOTREQD</strong> flag of the Active Directory User Account Control attribute. The\nacceptable values for this parameter are:</p>\n<ul>\n<li><code>$False</code> or <code>0</code></li>\n<li><code>$True</code> or <code>1</code></li>\n</ul>\n\n\n\t\t<h4 id=\"passwordnotrequired-properties\" data-chunk-ids=\"passwordnotrequired\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"passwordnotrequired\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"passwordnotrequired-sets\" data-chunk-ids=\"passwordnotrequired\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"passwordnotrequired\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-pobox\" data-chunk-ids=\"pobox\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-POBox</h3>\n\t\t<p>Specifies the user's post office box number.\nThis parameter sets the <strong>POBox</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is postOfficeBox.</p>\n\n\n\t\t<h4 id=\"pobox-properties\" data-chunk-ids=\"pobox\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"pobox\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"pobox-sets\" data-chunk-ids=\"pobox\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"pobox\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-postalcode\" data-chunk-ids=\"postalcode\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Postal<wbr>Code</h3>\n\t\t<p>Specifies the postal code or zip code. This parameter sets the <strong>PostalCode</strong> property of a user\nobject. The LDAP display name (<strong>ldapDisplayName</strong>) of this property is <code>postalCode</code>.</p>\n\n\n\t\t<h4 id=\"postalcode-properties\" data-chunk-ids=\"postalcode\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"postalcode\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"postalcode-sets\" data-chunk-ids=\"postalcode\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"postalcode\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-principalsallowedtodelegatetoaccount\" data-chunk-ids=\"principalsallowedtodelegatetoaccount\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Principals<wbr>Allowed<wbr>ToDelegate<wbr>ToAccount</h3>\n\t\t<p>Specifies an array of principal objects. This parameter sets the\n<strong>msDS-AllowedToActOnBehalfOfOtherIdentity</strong> attribute of a computer account object.</p>\n\n\n\t\t<h4 id=\"principalsallowedtodelegatetoaccount-properties\" data-chunk-ids=\"principalsallowedtodelegatetoaccount\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"principalsallowedtodelegatetoaccount\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><p><span class=\"no-loc xref\">ADPrincipal</span><span>[</span><span>]</span></p>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"principalsallowedtodelegatetoaccount-sets\" data-chunk-ids=\"principalsallowedtodelegatetoaccount\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"principalsallowedtodelegatetoaccount\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-profilepath\" data-chunk-ids=\"profilepath\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Profile<wbr>Path</h3>\n\t\t<p>Specifies a path to the user's profile.\nThis value can be a local absolute path or a Universal Naming Convention (UNC) path.\nThis parameter sets the <strong>ProfilePath</strong> property of the user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is profilePath.</p>\n\n\n\t\t<h4 id=\"profilepath-properties\" data-chunk-ids=\"profilepath\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"profilepath\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"profilepath-sets\" data-chunk-ids=\"profilepath\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"profilepath\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-remove\" data-chunk-ids=\"remove\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Remove</h3>\n\t\t<p>Specifies that the cmdlet remove values of an object property. Use this parameter to remove one or\nmore values of a property that cannot be modified using a cmdlet parameter. To remove an object\nproperty, you must use the LDAP display name. You can specify multiple values to a property by\nspecifying a comma-separated list of values, and more than one property by separating them using a\nsemicolon. If any of the properties have a null or empty value the cmdlet will return an error. The\nformat for this parameter is:</p>\n<p><code>-Remove @{Attribute1LDAPDisplayName=value1, value2, ...;   Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}</code></p>\n<p>When you use the <strong>Add</strong>, <strong>Remove</strong>, <strong>Replace</strong>, and <strong>Clear</strong> parameters together, the\nparameters are applied in the following sequence:</p>\n<ul>\n<li><strong>Remove</strong></li>\n<li><strong>Add</strong></li>\n<li><strong>Replace</strong></li>\n<li><strong>Clear</strong></li>\n</ul>\n\n\n\t\t<h4 id=\"remove-properties\" data-chunk-ids=\"remove\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"remove\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Hashtable</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"remove-sets\" data-chunk-ids=\"remove\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"remove\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-replace\" data-chunk-ids=\"replace\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Replace</h3>\n\t\t<p>Specifies values for an object property that will replace the current values. Use this parameter to\nreplace one or more values of a property that cannot be modified using a cmdlet parameter. To modify\nan object property, you must use the LDAP display name. You can specify multiple values to a\nproperty by specifying a comma-separated list of values, and more than one property by separating\nthem using a semicolon. If any of the properties have a null or empty value the cmdlet will return\nan error. The format for this parameter is:</p>\n<p><code>-Replace @{Attribute1LDAPDisplayName=value1, value2, ...;   Attribute2LDAPDisplayName=value1, value2, ...; AttributeNLDAPDisplayName=value1, value2, ...}</code></p>\n<p>When you use the <strong>Add</strong>, <strong>Remove</strong>, <strong>Replace</strong>, and <strong>Clear</strong> parameters together, the\noperations will be performed in the following order:</p>\n<ul>\n<li><strong>Remove</strong></li>\n<li><strong>Add</strong></li>\n<li><strong>Replace</strong></li>\n<li><strong>Clear</strong></li>\n</ul>\n\n\n\t\t<h4 id=\"replace-properties\" data-chunk-ids=\"replace\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"replace\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Hashtable</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"replace-sets\" data-chunk-ids=\"replace\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"replace\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-samaccountname\" data-chunk-ids=\"samaccountname\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Sam<wbr>Account<wbr>Name</h3>\n\t\t<p>Specifies the Security Account Manager (SAM) account name of the user, group, computer, or service\naccount. The maximum length of the description is 256 characters. To be compatible with older\noperating systems, create a SAM account name that is 20 characters or less. This parameter sets the\n<strong>SAMAccountName</strong> for an account object. The LDAP display name (<strong>ldapDisplayName</strong>) for this\nproperty is <code>sAMAccountName</code>.</p>\n<div class=\"NOTE\">\n<p>Note</p>\n<p>If the string value provided is not terminated with a <code>$</code> character, the system adds one if\nneeded.</p>\n</div>\n\n\n\t\t<h4 id=\"samaccountname-properties\" data-chunk-ids=\"samaccountname\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"samaccountname\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"samaccountname-sets\" data-chunk-ids=\"samaccountname\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"samaccountname\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-scriptpath\" data-chunk-ids=\"scriptpath\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Script<wbr>Path</h3>\n\t\t<p>Specifies a path to the user's log on script.\nThis value can be a local absolute path or a Universal Naming Convention (UNC) path.\nThis parameter sets the <strong>ScriptPath</strong> property of the user.\nThe LDAP display name (<strong>ldapDisplayName</strong>) for this property is scriptPath.</p>\n\n\n\t\t<h4 id=\"scriptpath-properties\" data-chunk-ids=\"scriptpath\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"scriptpath\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"scriptpath-sets\" data-chunk-ids=\"scriptpath\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"scriptpath\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-server\" data-chunk-ids=\"server\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Server</h3>\n\t\t<p>Specifies the AD DS instance to connect to, by providing one of the following values for a corresponding domain name or directory server.\nThe service may be any of the following: AD LDS, AD DS, or Active Directory snapshot instance.</p>\n<p>Specify the AD DS instance in one of the following ways:</p>\n<p>Domain name values:</p>\n<ul>\n<li>Fully qualified domain name</li>\n<li>NetBIOS name</li>\n</ul>\n<p>Directory server values:</p>\n<ul>\n<li>Fully qualified directory server name</li>\n<li>NetBIOS name</li>\n<li>Fully qualified directory server name and port</li>\n</ul>\n<p>The default value for this parameter is determined by one of the following methods in the order that\nthey are listed:</p>\n<ul>\n<li>By using the <strong>Server</strong> value from objects passed through the pipeline</li>\n<li>By using the server information associated with the AD DS Windows PowerShell provider drive, when\nthe cmdlet runs in that drive</li>\n<li>By using the domain of the computer running Windows PowerShell</li>\n</ul>\n\n\n\t\t<h4 id=\"server-properties\" data-chunk-ids=\"server\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"server\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"server-sets\" data-chunk-ids=\"server\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"server\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-serviceprincipalnames\" data-chunk-ids=\"serviceprincipalnames\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Service<wbr>Principal<wbr>Names</h3>\n\t\t<p>Specifies the service principal names for the account. This parameter sets the\n<strong>ServicePrincipalNames</strong> property of the account. The LDAP display name (<strong>ldapDisplayName</strong>) for\nthis property is <code>servicePrincipalName</code>. This parameter uses the following syntax to add, remove,\nreplace or clear service principal name values.</p>\n<p>Syntax:</p>\n<p>To add values:</p>\n<p><code>-ServicePrincipalNames @{Add=value1,value2,...}</code></p>\n<p>To remove values:</p>\n<p><code>-ServicePrincipalNames @{Remove=value3,value4,...}</code></p>\n<p>To replace values:</p>\n<p><code>-ServicePrincipalNames @{Replace=value1,value2,...}</code></p>\n<p>To clear all values:</p>\n<p><code>-ServicePrincipalNames $null</code></p>\n<p>You can specify more than one change by using a list separated by semicolons. For example, use the\nfollowing syntax to add and remove service principal names.</p>\n<p><code>@{Add=value1,value2,...};@{Remove=value3,value4,...}</code></p>\n<p>The operators will be applied in the following sequence:</p>\n<ul>\n<li>Remove</li>\n<li>Add</li>\n<li>Replace</li>\n</ul>\n<p>The following example shows how to add and remove service principal names.</p>\n<p><code>-ServicePrincipalNames-@{Add=\"SQLservice\\accounting.corp.contoso.com:1456\"};{Remove=\"SQLservice\\finance.corp.contoso.com:1456\"}</code></p>\n\n\n\t\t<h4 id=\"serviceprincipalnames-properties\" data-chunk-ids=\"serviceprincipalnames\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"serviceprincipalnames\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Hashtable</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"serviceprincipalnames-sets\" data-chunk-ids=\"serviceprincipalnames\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"serviceprincipalnames\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-smartcardlogonrequired\" data-chunk-ids=\"smartcardlogonrequired\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Smartcard<wbr>Logon<wbr>Required</h3>\n\t\t<p>Indicates whether a smart card is required to logon. This parameter sets the\n<strong>SmartCardLoginRequired</strong> property for a user. This parameter also sets the\n<strong>ADS_UF_SMARTCARD_REQUIRED</strong> flag of the Active Directory User Account Control attribute. The\nacceptable values for this parameter are:</p>\n<ul>\n<li>$False or 0</li>\n<li>$True or 1</li>\n</ul>\n\n\n\t\t<h4 id=\"smartcardlogonrequired-properties\" data-chunk-ids=\"smartcardlogonrequired\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"smartcardlogonrequired\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"smartcardlogonrequired-sets\" data-chunk-ids=\"smartcardlogonrequired\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"smartcardlogonrequired\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-state\" data-chunk-ids=\"state\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-State</h3>\n\t\t<p>Specifies the user's state or province.\nThis parameter sets the <strong>State</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is st.</p>\n\n\n\t\t<h4 id=\"state-properties\" data-chunk-ids=\"state\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"state\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"state-sets\" data-chunk-ids=\"state\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"state\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-streetaddress\" data-chunk-ids=\"streetaddress\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Street<wbr>Address</h3>\n\t\t<p>Specifies the user's street address.\nThis parameter sets the <strong>StreetAddress</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is streetAddress.</p>\n\n\n\t\t<h4 id=\"streetaddress-properties\" data-chunk-ids=\"streetaddress\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"streetaddress\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"streetaddress-sets\" data-chunk-ids=\"streetaddress\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"streetaddress\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-surname\" data-chunk-ids=\"surname\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Surname</h3>\n\t\t<p>Specifies the user's last name or surname.\nThis parameter sets the <strong>Surname</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is sn.</p>\n\n\n\t\t<h4 id=\"surname-properties\" data-chunk-ids=\"surname\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"surname\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"surname-sets\" data-chunk-ids=\"surname\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"surname\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-title\" data-chunk-ids=\"title\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Title</h3>\n\t\t<p>Specifies the user's title.\nThis parameter sets the <strong>Title</strong> property of a user object.\nThe LDAP display name (<strong>ldapDisplayName</strong>) of this property is title.</p>\n\n\n\t\t<h4 id=\"title-properties\" data-chunk-ids=\"title\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"title\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"title-sets\" data-chunk-ids=\"title\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"title\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-trustedfordelegation\" data-chunk-ids=\"trustedfordelegation\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-Trusted<wbr>For<wbr>Delegation</h3>\n\t\t<p>Specifies whether an account is trusted for Kerberos delegation. A service that runs under an\naccount that is trusted for Kerberos delegation can assume the identity of a client requesting the\nservice. This parameter sets the <strong>TrustedForDelegation</strong> property of an account object. This value\nalso sets the <strong>ADS_UF_TRUSTED_FOR_DELEGATION</strong> flag of the Active Directory User Account Control\nattribute. The acceptable values for this parameter are:</p>\n<ul>\n<li><code>$False</code> or <code>0</code></li>\n<li><code>$True</code> or <code>1</code></li>\n</ul>\n\n\n\t\t<h4 id=\"trustedfordelegation-properties\" data-chunk-ids=\"trustedfordelegation\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"trustedfordelegation\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">Boolean</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"trustedfordelegation-sets\" data-chunk-ids=\"trustedfordelegation\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"trustedfordelegation\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-userprincipalname\" data-chunk-ids=\"userprincipalname\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-User<wbr>Principal<wbr>Name</h3>\n\t\t<p>Specifies a user principal name (UPN) in the format <code>&lt;user&gt;@&lt;DNS-domain-name&gt;</code>. A UPN is a friendly\nname assigned by an administrator that is shorter than the LDAP distinguished name used by the\nsystem and easier to remember. The UPN is independent of the user object's distinguished name, so a\nuser object can be moved or renamed without affecting the user logon name. When logging on using a\nUPN, users don't have to choose a domain from a list on the logon dialog box.</p>\n\n\n\t\t<h4 id=\"userprincipalname-properties\" data-chunk-ids=\"userprincipalname\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"userprincipalname\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">String</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>None</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"userprincipalname-sets\" data-chunk-ids=\"userprincipalname\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"userprincipalname\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\tIdentity \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"-whatif\" data-chunk-ids=\"whatif\" class=\"font-family-monospace margin-top-lg margin-bottom-md\">-What<wbr>If</h3>\n\t\t<p>Shows what would happen if the cmdlet runs.\nThe cmdlet is not run.</p>\n\n\n\t\t<h4 id=\"whatif-properties\" data-chunk-ids=\"whatif\">Parameter properties</h4>\n\t\t<table data-chunk-ids=\"whatif\" class=\"table\">\n\t\t\t\t<tbody><tr><td>Type:</td><td><span class=\"no-loc xref\">SwitchParameter</span>\n</td></tr>\n\t\t\t\t<tr><td>Default value:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Supports wildcards:</td><td>False</td></tr>\n\t\t\t\t<tr><td>DontShow:</td><td>False</td></tr>\n\t\t\t\t<tr><td>Aliases:</td><td>wi</td></tr>\n\t\t</tbody></table>\n\n\t\t<h4 id=\"whatif-sets\" data-chunk-ids=\"whatif\">Parameter sets</h4>\n\t\t\t<details class=\"margin-top-sm\" data-chunk-ids=\"whatif\" open=\"\">\n\t\t\t\t<summary class=\"list-style-none link-button\">\n\t\t\t\t\t(All) \n\t\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t</summary>\n\t\t\t\t<table class=\"table\">\n\t\t\t\t\t\t<tbody><tr><td>Position:</td><td>Named</td></tr>\n\t\t\t\t\t\t<tr><td>Mandatory:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from pipeline by property name:</td><td>False</td></tr>\n\t\t\t\t\t\t<tr><td>Value from remaining arguments:</td><td>False</td></tr>\n\t\t\t\t</tbody></table>\n\t\t\t</details>\n\t\t<h3 id=\"common-parameters\" data-no-chunk=\"\">CommonParameters</h3>\n\t\t<div data-no-chunk=\"\">\n\t\t\t<p>This cmdlet supports the common parameters: -Debug, -ErrorAction, -ErrorVariable,\n-InformationAction, -InformationVariable, -OutBuffer, -OutVariable, -PipelineVariable,\n-ProgressAction, -Verbose, -WarningAction, and -WarningVariable. For more information, see\n<a href=\"https://go.microsoft.com/fwlink/?LinkID=113216\" data-linktype=\"external\">about_CommonParameters</a>.</p>\n\n\t\t</div>\n\n\t<h2 id=\"inputs\" data-chunk-ids=\"inputs\">Inputs</h2>\n\t\t\t<h3 id=\"input-1\" data-chunk-ids=\"inputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">None or Microsoft.ActiveDirectory.Management.ADUser</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"inputs\">\n\t\t\t\t<p>A user object is received by the <strong>Identity</strong> parameter.</p>\n<p>A user object that was retrieved by using the <code>Get-ADUser</code> cmdlet and then modified is received by\nthe <strong>Instance</strong> parameter.</p>\n\n\t\t\t</div>\n\n\t<h2 id=\"outputs\" data-chunk-ids=\"outputs\">Outputs</h2>\n\t\t\t<h3 id=\"output-1\" data-chunk-ids=\"outputs\" class=\"break-text font-size-xl\"><span class=\"no-loc xref\">None or Microsoft.ActiveDirectory.Management.ADUser</span>\n</h3>\n\t\t\t<div data-chunk-ids=\"outputs\">\n\t\t\t\t<p>Returns the modified user object when the <strong>PassThru</strong> parameter is specified.\nBy default, this cmdlet does not generate any output.</p>\n\n\t\t\t</div>\n\n\t<h2 id=\"notes\" data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">Notes</h2>\n\t<div data-chunk-ids=\"inputs,outputs,identity,instance,example-1-set-properties-for-a-user,example-2-set-properties-for-multiple-users,example-3-set-properties,example-4-modify-a-user-othermailbox-property,example-5-set-user-properties-to-a-local-instance,example-6-set-attributes-for-a-user,example-7-set-a-property-for-a-user,example-8-get-a-user-and-set-a-property,accountexpirationdate,accountnotdelegated,add,allowreversiblepasswordencryption,authenticationpolicy,authenticationpolicysilo,authtype,cannotchangepassword,certificates,changepasswordatlogon,city,clear,company,compoundidentitysupported,confirm,country,credential,department,description,displayname,division,emailaddress,employeeid,employeenumber,enabled,fax,givenname,homedirectory,homedrive,homepage,homephone,identity,initials,instance,kerberosencryptiontype,logonworkstations,manager,mobilephone,office,officephone,organization,othername,partition,passthru,passwordneverexpires,passwordnotrequired,pobox,postalcode,principalsallowedtodelegatetoaccount,profilepath,remove,replace,samaccountname,scriptpath,server,serviceprincipalnames,smartcardlogonrequired,state,streetaddress,surname,title,trustedfordelegation,userprincipalname,whatif\">\n\t\t<ul>\n<li>This cmdlet does not work with an Active Directory snapshot.</li>\n<li>This cmdlet does not work with a read-only domain controller.</li>\n</ul>\n\n\t</div>\n\n\t<h2 id=\"related-links\" data-no-chunk=\"\">Related Links</h2>\n\t<ul data-no-chunk=\"\">\n\t\t\t<li><a href=\"get-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Get-ADUser</a></li>\n\t\t\t<li><a href=\"new-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">New-ADUser</a></li>\n\t\t\t<li><a href=\"remove-aduser?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Remove-ADUser</a></li>\n\t\t\t<li><a href=\"set-adaccountcontrol?view=windowsserver2025-ps\" data-linktype=\"relative-path\">Set-ADAccountControl</a></li>\n\t</ul>\n</div>\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--inline-notifications\"\n\t\t\tclass=\"margin-block-xs\"\n\t\t\tdata-bi-name=\"inline-notification\"\n\t\t></div>\n\t \n\t\t<div\n\t\t\tid=\"assertive-live-region\"\n\t\t\trole=\"alert\"\n\t\t\taria-live=\"assertive\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\t<div\n\t\t\tid=\"polite-live-region\"\n\t\t\trole=\"status\"\n\t\t\taria-live=\"polite\"\n\t\t\tclass=\"visually-hidden\"\n\t\t\taria-relevant=\"additions\"\n\t\t\taria-atomic=\"true\"\n\t\t></div>\n\t\n\t\t\t\t\t\n\t\t<!-- feedback section -->\n\t\t<section\n\t\t\tid=\"site-user-feedback-footer\"\n\t\t\tclass=\"font-size-sm margin-top-md display-none-print display-none-desktop\"\n\t\t\tdata-test-id=\"site-user-feedback-footer\"\n\t\t\tdata-bi-name=\"site-feedback-section\"\n\t\t>\n\t\t\t<hr class=\"hr\" />\n\t\t\t<h2 id=\"ms--feedback\" class=\"title is-3\">Feedback</h2>\n\t\t\t<div class=\"display-flex flex-wrap-wrap align-items-center\">\n\t\t\t\t<p class=\"font-weight-semibold margin-xxs margin-left-none\">\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t</p>\n\t\t\t\t<div class=\"buttons\">\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-yes\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>Yes</span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<button\n\t\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\t\tdata-test-id=\"footer-rating-no\"\n\t\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span>No</span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</section>\n\t\t<!-- end feedback section -->\n\t\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t</div>\n\t\t\t\n\t\t<div\n\t\t\tid=\"action-panel\"\n\t\t\trole=\"region\"\n\t\t\taria-label=\"Action Panel\"\n\t\t\tclass=\"action-panel\"\n\t\t\ttabindex=\"-1\"\n\t\t></div>\n\t\n\t\t\n\t\t\t\t</main>\n\t\t\t\t<aside\n\t\t\t\t\tid=\"layout-body-aside\"\n\t\t\t\t\tclass=\"layout-body-aside \"\n\t\t\t\t\tdata-bi-name=\"aside\"\n\t\t\t  >\n\t\t\t\t\t\n\t\t<div\n\t\t\tid=\"ms--additional-resources\"\n\t\t\tclass=\"right-container padding-sm display-none display-block-desktop height-full\"\n\t\t\tdata-bi-name=\"pageactions\"\n\t\t\trole=\"complementary\"\n\t\t\taria-label=\"Additional resources\"\n\t\t>\n\t\t\t<div id=\"affixed-right-container\" data-bi-name=\"right-column\">\n\t\t\t\t\n\t\t<nav\n\t\t\tid=\"side-doc-outline\"\n\t\t\tclass=\"doc-outline border-bottom padding-bottom-xs margin-bottom-xs\"\n\t\t\tdata-bi-name=\"intopic toc\"\n\t\t\taria-label=\"In this article\"\n\t\t>\n\t\t\t<h3>In this article</h3>\n\t\t</nav>\n\t\n\t\t\t\t<!-- Feedback -->\n\t\t\t\t\n\t\t<section\n\t\t\tid=\"ms--site-user-feedback-right-rail\"\n\t\t\tclass=\"font-size-sm display-none-print\"\n\t\t\tdata-test-id=\"site-user-feedback-right-rail\"\n\t\t\tdata-bi-name=\"site-feedback-right-rail\"\n\t\t>\n\t\t\t<p class=\"font-weight-semibold margin-bottom-xs\">Was this page helpful?</p>\n\t\t\t<div class=\"buttons\">\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button like button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-yes\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-yes\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-yes\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-like\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>Yes</span>\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass=\"thumb-rating-button dislike button button-primary button-sm\"\n\t\t\t\t\tdata-test-id=\"right-rail-rating-no\"\n\t\t\t\t\tdata-binary-rating-response=\"rating-no\"\n\t\t\t\t\ttype=\"button\"\n\t\t\t\t\ttitle=\"This article is not helpful\"\n\t\t\t\t\tdata-bi-name=\"button-rating-no\"\n\t\t\t\t\taria-pressed=\"false\"\n\t\t\t\t>\n\t\t\t\t\t<span class=\"icon\" aria-hidden=\"true\">\n\t\t\t\t\t\t<span class=\"docon docon-dislike\"></span>\n\t\t\t\t\t</span>\n\t\t\t\t\t<span>No</span>\n\t\t\t\t</button>\n\t\t\t</div>\n\t\t</section>\n\t\n\t\t\t</div>\n\t\t</div>\n\t\n\t\t\t  </aside> <section\n\t\t\t\t\tid=\"layout-body-flyout\"\n\t\t\t\t\tclass=\"layout-body-flyout \"\n\t\t\t\t\tdata-bi-name=\"flyout\"\n\t\t\t  >\n\t\t\t\t\t <div\n\tclass=\"height-full border-left background-color-body-medium\"\n\tid=\"ask-learn-flyout\"\n></div>\n\t\t\t  </section> <div class=\"layout-body-footer \" data-bi-name=\"layout-footer\">\n\t\t<footer\n\t\t\tid=\"footer\"\n\t\t\tdata-test-id=\"footer\"\n\t\t\tdata-bi-name=\"footer\"\n\t\t\tclass=\"footer-layout has-padding has-default-focus border-top  uhf-container\"\n\t\t\trole=\"contentinfo\"\n\t\t>\n\t\t\t<div class=\"display-flex gap-xs flex-wrap-wrap is-full-height padding-right-lg-desktop\">\n\t\t\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"#\"\n\t\t\tdata-bi-name=\"select-locale\"\n\t\t\tclass=\"locale-selector-link flex-shrink-0 button button-sm button-clear external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t><span class=\"icon\" aria-hidden=\"true\"\n\t\t\t\t><span class=\"docon docon-world\"></span></span\n\t\t\t><span class=\"local-selector-link-text\">en-us</span></a\n\t\t>\n\t\n\t\t\t\t<div class=\"ccpa-privacy-link\" data-ccpa-privacy-link hidden>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://aka.ms/yourcaliforniaprivacychoices\"\n\t\t\tdata-bi-name=\"your-privacy-choices\"\n\t\t\tclass=\"button button-sm button-clear flex-shrink-0 external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>\n\t\t<svg\n\t\t\txmlns=\"http://www.w3.org/2000/svg\"\n\t\t\tviewBox=\"0 0 30 14\"\n\t\t\txml:space=\"preserve\"\n\t\t\theight=\"16\"\n\t\t\twidth=\"43\"\n\t\t\taria-hidden=\"true\"\n\t\t\tfocusable=\"false\"\n\t\t>\n\t\t\t<path\n\t\t\t\td=\"M7.4 12.8h6.8l3.1-11.6H7.4C4.2 1.2 1.6 3.8 1.6 7s2.6 5.8 5.8 5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M22.6 0H7.4c-3.9 0-7 3.1-7 7s3.1 7 7 7h15.2c3.9 0 7-3.1 7-7s-3.2-7-7-7zm-21 7c0-3.2 2.6-5.8 5.8-5.8h9.9l-3.1 11.6H7.4c-3.2 0-5.8-2.6-5.8-5.8z\"\n\t\t\t\tstyle=\"fill-rule:evenodd;clip-rule:evenodd;fill:#06f\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M24.6 4c.******* 0 .8L22.5 7l2.2 2.2c.******* 0 .8-.2.2-.6.2-.8 0l-2.2-2.2-2.2 2.2c-.2.2-.6.2-.8 0-.2-.2-.2-.6 0-.8L20.8 7l-2.2-2.2c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0l2.2 2.2L23.8 4c.2-.2.6-.2.8 0z\"\n\t\t\t\tstyle=\"fill:#fff\"\n\t\t\t></path>\n\t\t\t<path\n\t\t\t\td=\"M12.7 4.1c.*******.1.8L8.6 9.8c-.1.1-.2.2-.3.2-.2.1-.5.1-.7-.1L5.4 7.7c-.2-.2-.2-.6 0-.8.2-.2.6-.2.8 0L8 8.6l3.8-4.5c.2-.2.6-.2.9 0z\"\n\t\t\t\tstyle=\"fill:#06f\"\n\t\t\t></path>\n\t\t</svg>\n\t\n\t\t\t<span>Your Privacy Choices</span></a\n\t\t>\n\t\n\t</div>\n\t\t\t\t<div class=\"flex-shrink-0\">\n\t\t<div class=\"dropdown has-caret-up\">\n\t\t\t<button\n\t\t\t\tdata-test-id=\"theme-selector-button\"\n\t\t\t\tclass=\"dropdown-trigger button button-clear button-sm has-inner-focus theme-dropdown-trigger\"\n\t\t\t\taria-controls=\"{{ themeMenuId }}\"\n\t\t\t\taria-expanded=\"false\"\n\t\t\t\ttitle=\"Theme\"\n\t\t\t\tdata-bi-name=\"theme\"\n\t\t\t>\n\t\t\t\t<span class=\"icon\">\n\t\t\t\t\t<span class=\"docon docon-sun\" aria-hidden=\"true\"></span>\n\t\t\t\t</span>\n\t\t\t\t<span>Theme</span>\n\t\t\t\t<span class=\"icon expanded-indicator\" aria-hidden=\"true\">\n\t\t\t\t\t<span class=\"docon docon-chevron-down-light\"></span>\n\t\t\t\t</span>\n\t\t\t</button>\n\t\t\t<div class=\"dropdown-menu\" id=\"{{ themeMenuId }}\" role=\"menu\">\n\t\t\t\t<ul class=\"theme-selector padding-xxs\" data-test-id=\"theme-dropdown-menu\">\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"light\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-light margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"theme-selector-icon border display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Light </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"dark\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-dark margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> Dark </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class=\"theme display-block\">\n\t\t\t\t\t\t<button\n\t\t\t\t\t\t\tclass=\"button button-clear button-sm theme-control button-block justify-content-flex-start text-align-left\"\n\t\t\t\t\t\t\tdata-theme-to=\"high-contrast\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<span class=\"theme-high-contrast margin-right-xxs\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass=\"border theme-selector-icon display-inline-block has-body-background\"\n\t\t\t\t\t\t\t\t\taria-hidden=\"true\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<svg class=\"svg\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 22 14\">\n\t\t\t\t\t\t\t\t\t\t<rect width=\"22\" height=\"14\" class=\"has-fill-body-background\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"5\" width=\"12\" height=\"4\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"8\" y=\"2\" width=\"2\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"11\" y=\"2\" width=\"3\" height=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"1\" y=\"1\" width=\"2\" height=\"2\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"5\" y=\"10\" width=\"7\" height=\"2\" rx=\"0.3\" class=\"has-fill-primary\" />\n\t\t\t\t\t\t\t\t\t\t<rect x=\"19\" y=\"1\" width=\"2\" height=\"2\" rx=\"1\" class=\"has-fill-secondary\" />\n\t\t\t\t\t\t\t\t\t</svg>\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t<span role=\"menuitem\"> High contrast </span>\n\t\t\t\t\t\t</button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>\n\t</div>\n\t\t\t</div>\n\t\t\t<ul class=\"links\" data-bi-name=\"footerlinks\">\n\t\t\t\t<li class=\"manage-cookies-holder\" hidden=\"\"></li>\n\t\t\t\t<li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/principles-for-ai-generated-content\"\n\t\t\tdata-bi-name=\"aiDisclaimer\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>AI Disclaimer</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/previous-versions/\"\n\t\t\tdata-bi-name=\"archivelink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Previous Versions</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://techcommunity.microsoft.com/t5/microsoft-learn-blog/bg-p/MicrosoftLearnBlog\"\n\t\t\tdata-bi-name=\"bloglink\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Blog</a\n\t\t>\n\t\n\t</li> <li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/contribute\"\n\t\t\tdata-bi-name=\"contributorGuide\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Contribute</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://go.microsoft.com/fwlink/?LinkId=521839\"\n\t\t\tdata-bi-name=\"privacy\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Privacy</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://learn.microsoft.com/en-us/legal/termsofuse\"\n\t\t\tdata-bi-name=\"termsofuse\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Terms of Use</a\n\t\t>\n\t\n\t</li><li>\n\t\t\n\t\t<a\n\t\t\tdata-mscc-ic=\"false\"\n\t\t\thref=\"https://www.microsoft.com/legal/intellectualproperty/Trademarks/\"\n\t\t\tdata-bi-name=\"trademarks\"\n\t\t\tclass=\" external-link-indicator\"\n\t\t\tid=\"\"\n\t\t\ttitle=\"\"\n\t\t\t>Trademarks</a\n\t\t>\n\t\n\t</li>\n\t\t\t\t<li>&copy; Microsoft 2025</li>\n\t\t\t</ul>\n\t\t</footer>\n\t</footer>\n\t\t\t</body>\n\t\t</html>"}, {"RelevanceScore": 1, "CreatedAt": "2025-07-28T10:10:18Z", "PatternType": "cmdlet_usage", "Id": "pattern_general_ad_6773a4eb", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:18Z", "BestPractices": [], "Title": "Stack Overflow: How to get all groups that a user is a member of?", "CodeTemplate": "G", "CommonMistakes": [], "Domain": "general_ad", "RequiredParameters": "\"Get-ADGroupMember\\u003c/code\\u003e\\u003c/a\\u003e cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?\\u003c/p\\u003e\"", "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "general_ad", "read", "windows", "powershell", "cmd", "active-directory", "powershell-2.0"], "Abstract": "<p>PowerShell's <a href=\"https://technet.microsoft.com/en-us/library/ee617193.aspx\" rel=\"noreferrer\"><code>Get-ADGroupMember</code></a> cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?</p>\n...", "Sources": [{"SourceType": "stackoverflow", "CredibilityScore": 0.8, "PublishedAt": "2025-07-28T10:10:18Z", "Url": "https://stackoverflow.com/questions/5072996/how-to-get-all-groups-that-a-user-is-a-member-of", "ScrapedAt": "2025-07-28T10:10:18Z", "Id": "877ace1f-1841-45b4-b1c0-b2608fdd2219", "Author": "Stack Overflow Community", "Title": "Stack Overflow: How to get all groups that a user is a member of?"}], "Content": "<p>PowerShell's <a href=\"https://technet.microsoft.com/en-us/library/ee617193.aspx\" rel=\"noreferrer\"><code>Get-ADGroupMember</code></a> cmdlet returns members of a specific group. Is there a cmdlet or property to get all the groups that a particular user is a member of?</p>\n"}, {"RelevanceScore": 0.68, "CreatedAt": "2025-07-28T10:10:19Z", "PatternType": "workflow", "Id": "pattern_general_ad_b1fabf8b", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:19Z", "BestPractices": [], "Title": "Stack Overflow: Powershell script to see currently logged in users (domain and machine) + status (active, idle, away)", "CodeTemplate": "Get-WmiObject -Class win32_computersystem", "CommonMistakes": [], "Domain": "general_ad", "RequiredParameters": {}, "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "general_ad", "read", "powershell", "active-directory", "powershell-2.0", "powershell-3.0", "windows-server-2012"], "Abstract": "<p>I am searching for a simple command to see logged on users on server.\nI know this one :</p>\n\n<pre><code>Get-WmiObject -Class win32_computersystem\n</code></pre>\n\n<p>but this will not provide me the info I need.\nIt returns :\ndomain\nManufactureer\nModel\nName (Machine name)\nPrimaryOwnerName\nTotalPhysicalMemory</p>\n\n<p>I run Powershell 3.0 on a Windows 2012 server.</p>\n\n<p>Also </p>\n\n<pre><code>Get-WmiObject Win32_LoggedOnUser -ComputerName $Computer | Select Antecedent -Unique\n</code></pre>\n\n<p>gi...", "Sources": [{"SourceType": "stackoverflow", "CredibilityScore": 0.8, "PublishedAt": "2025-07-28T10:10:19Z", "Url": "https://stackoverflow.com/questions/23219718/powershell-script-to-see-currently-logged-in-users-domain-and-machine-status", "ScrapedAt": "2025-07-28T10:10:19Z", "Id": "ff4f8210-6a73-48c6-86bb-549f9b0c9c72", "Author": "Stack Overflow Community", "Title": "Stack Overflow: Powershell script to see currently logged in users (domain and machine) + status (active, idle, away)"}], "Content": "<p>I am searching for a simple command to see logged on users on server.\nI know this one :</p>\n\n<pre><code>Get-WmiObject -Class win32_computersystem\n</code></pre>\n\n<p>but this will not provide me the info I need.\nIt returns :\ndomain\nManufactureer\nModel\nName (Machine name)\nPrimaryOwnerName\nTotalPhysicalMemory</p>\n\n<p>I run Powershell 3.0 on a Windows 2012 server.</p>\n\n<p>Also </p>\n\n<pre><code>Get-WmiObject Win32_LoggedOnUser -ComputerName $Computer | Select Antecedent -Unique\n</code></pre>\n\n<p>gives me not the exact answers I need.\nI would love to see as well the idle time, or if they are active or away.</p>\n"}, {"RelevanceScore": 0.67, "CreatedAt": "2025-07-28T10:10:20Z", "PatternType": "common_mistake", "Id": "pattern_general_ad_19cd731e", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:20Z", "BestPractices": [], "Title": "Stack Overflow: Powershell: A positional parameter cannot be found that accepts argument &quot;xxx&quot;", "CodeTemplate": "I", "CommonMistakes": [], "Domain": "general_ad", "RequiredParameters": "[\"Get-ADUser -SearchBase \\u0026quot;ou=Testing,ou=Users,dc=my,dc=domain\\u0026quot; -Filter * -Properties *\",\"Set-ADUser $user -userPrincipalName = $newname\"]", "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 1, "Tags": ["powershell", "active-directory", "general_ad", "read", "powershell", "active-directory"], "Abstract": "<p>I am trying to understand what this error actually means. So far a search of similar help requests for this error range from missing parameters, missing pipes, use of single or multi-lines, and also concatenation issues but none of the answers seem to give a definitive reason. So I assume the issue is code format (which makes it a lot harder to track down).</p>\n<p>This is my script which I am writing to rename active directory users per target OU from whatever format they are now into a first...", "Sources": [{"SourceType": "stackoverflow", "CredibilityScore": 0.8, "PublishedAt": "2025-07-28T10:10:20Z", "Url": "https://stackoverflow.com/questions/35433151/powershell-a-positional-parameter-cannot-be-found-that-accepts-argument-xxx", "ScrapedAt": "2025-07-28T10:10:20Z", "Id": "6b411d92-a769-426c-beb0-ab5784e3bc68", "Author": "Stack Overflow Community", "Title": "Stack Overflow: Powershell: A positional parameter cannot be found that accepts argument &quot;xxx&quot;"}], "Content": "<p>I am trying to understand what this error actually means. So far a search of similar help requests for this error range from missing parameters, missing pipes, use of single or multi-lines, and also concatenation issues but none of the answers seem to give a definitive reason. So I assume the issue is code format (which makes it a lot harder to track down).</p>\n<p>This is my script which I am writing to rename active directory users per target OU from whatever format they are now into a firstname.surname format.</p>\n<p>I have created a test OU in AD with some users who will trigger errors and some that will not. However, the users that should not give me an error are giving me the &quot;a positional parameter cannot be found that accepts argument &quot;firstname.surname&quot;</p>\n<p>I cannot see what is wrong with the script but hopefully, someone can give me some pointers.</p>\n<pre><code>Import-Module ActiveDirectory\n\n$users = $null\n\n$users = Get-ADUser -SearchBase &quot;ou=Testing,ou=Users,dc=my,dc=domain&quot; -Filter * -Properties *\nforeach ($user in $users) {\n    Write-Host &quot;Processing... $($user)&quot;\n    $newname = $null\n\n    # Check first/last name is set\n    if (!$user.givenName -or !$user.Surname) {\n        Write-Host &quot;$($user) does not have first name or last name set. Please correct, skipping user.&quot;\n        continue\n    } else {\n        $newname = (&quot;$($user.givenName).$($user.Surname)&quot;)\n\n        #Check if new username already exists\n        if (dsquery user -samid $newname) {\n            Write-Host &quot;$($user) requires altered username with initial.&quot;\n\n            if (!$user.Initials) {\n                Write-Host &quot;$($user) does not have any initials set. Please correct, skipping user.&quot;\n                continue\n            }\n\n            $newname = (&quot;$($user.givenName)$($user.Initials).$($user.Surname)&quot;)\n\n            #Check if altered new username already exists\n            if (dsquery user -samid $newname) {\n                Write-Host &quot;$($user) requires manual change. Please correct, skipping user.&quot;\n                continue\n            }\n        }\n\n        try {\n            #Change UPN\n            Set-ADUser $user -userPrincipalName = $newname\n            #Change DN\n            Rename-ADObject -identity $user -Newname $newname\n        } catch {\n            Write-Host &quot;Error when renaming $($user). Error is: $($_.Exception.Message). User requires manual change. Please correct, skipping user.&quot;\n            continue\n        }\n    }\n}\n</code></pre>\n"}, {"RelevanceScore": 0.9928073618190603, "CreatedAt": "2025-07-28T10:10:22Z", "PatternType": "workflow", "Id": "pattern_general_ad_ed9e88e3", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:22Z", "BestPractices": [], "Title": "GitHub: samratash<PERSON>/nishang", "CodeTemplate": "", "CommonMistakes": [], "Domain": "general_ad", "RequiredParameters": {}, "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 0.75, "Tags": ["powershell", "active-directory", "general_ad", "read", "activedirectory", "hacking", "infosec", "nishang", "penetration-testing", "powershell", "red-team", "redteam", "security", "github", "repository", "powershell"], "Abstract": "# samratashok/nishang\n\n**Description:** Nishang - Offensive PowerShell for red team, penetration testing and offensive security. \n\n**Stars:** 9358\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/nishang...", "Sources": [{"SourceType": "github", "CredibilityScore": 0.75, "PublishedAt": "2025-07-28T10:10:22Z", "Url": "https://github.com/samratashok/nishang", "ScrapedAt": "2025-07-28T10:10:22Z", "Id": "64f2539e-9773-4be4-85c7-5a2817b71b36", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title": "GitHub: samratash<PERSON>/nishang"}], "Content": "# samratashok/nishang\n\n**Description:** Nishang - Offensive PowerShell for red team, penetration testing and offensive security. \n\n**Stars:** 9358\n**Language:** PowerShell\n**URL:** https://github.com/samratashok/nishang"}, {"RelevanceScore": 0.7551938720483895, "CreatedAt": "2025-07-28T10:10:23Z", "PatternType": "workflow", "Id": "pattern_group_management_ab9ec789", "Operation": "read", "UpdatedAt": "2025-07-28T10:10:23Z", "BestPractices": [], "Title": "GitHub: EvotecIT/GPOZaurr", "CodeTemplate": "", "CommonMistakes": [], "Domain": "group_management", "RequiredParameters": {}, "PerformanceTips": [], "CreatedBy": "PowerShellScraper", "UsageCount": 0, "CredibilityScore": 0.75, "Tags": ["powershell", "active-directory", "group_management", "read", "activedirectory", "gpo", "group-policy", "hacktoberfest", "powershell", "github", "repository", "powershell"], "Abstract": "# EvotecIT/GPOZaurr\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**Stars:** 1048\n**Language:** PowerShell\n**URL:** https://github.com/EvotecIT/GPOZaurr...", "Sources": [{"SourceType": "github", "CredibilityScore": 0.75, "PublishedAt": "2025-07-28T10:10:23Z", "Url": "https://github.com/EvotecIT/GPOZaurr", "ScrapedAt": "2025-07-28T10:10:23Z", "Id": "1ae36830-fdf8-4066-b0d7-8e53f86f9116", "Author": "EvotecIT", "Title": "GitHub: EvotecIT/GPOZaurr"}], "Content": "# EvotecIT/GPOZaurr\n\n**Description:** Group Policy Eater is a PowerShell module that aims to gather information about Group Policies but also allows fixing issues that you may find in them. \n\n**Stars:** 1048\n**Language:** PowerShell\n**URL:** https://github.com/EvotecIT/GPOZaurr"}], "SourceType": "comprehensive", "TotalPatterns": 8}